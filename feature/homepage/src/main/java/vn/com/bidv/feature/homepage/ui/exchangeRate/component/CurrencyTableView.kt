package vn.com.bidv.feature.homepage.ui.exchangeRate.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.domain.model.ExchangeRateDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun CurrencyTableView(
    exchangeRateList: List<ExchangeRateDMO> = emptyList(),
    limitedShownData: Int = 6,
    firstColumnWeight: Float = 0.24f,
    secondColumnWeight: Float = 0.38f,
) {
    Column(modifier = Modifier.padding(horizontal = IBSpacing.spacingM)) {
        TableHeader(
            firstColumnWeight = firstColumnWeight,
            secondColumnWeight = secondColumnWeight,
        )
        TableBody(
            exchangeRateList = exchangeRateList,
            limitedShownData = limitedShownData,
            firstColumnWeight = firstColumnWeight,
            secondColumnWeight = secondColumnWeight,
        )
    }
}

@Composable
private fun TableHeader(
    modifier: Modifier = Modifier,
    firstColumnWeight: Float,
    secondColumnWeight: Float,
) {
    val colorScheme = LocalColorScheme.current
    val headerStyle = LocalTypography.current.bodyBody_m.copy(color = colorScheme.contentMainSecondary)
    Row(
        modifier =
            Modifier
                .background(colorScheme.bgMainSecondary)
                .padding(IBSpacing.spacingS)
                .then(modifier),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        TableCellText(
            text = stringResource(R.string.ngoai_te),
            weight = firstColumnWeight,
            style = headerStyle,
            textAlign = TextAlign.Start,
        )
        TableCellText(
            text = stringResource(R.string.mua_nchuyen_khoan),
            weight = secondColumnWeight,
            style = headerStyle,
            textAlign = TextAlign.End,
            modifier = Modifier.padding(end = IBSpacing.spacingS),
        )
        TableCellText(
            text = stringResource(R.string.ban_nchuyen_khoan),
            weight = 1 - firstColumnWeight - secondColumnWeight,
            style = headerStyle,
            textAlign = TextAlign.End,
        )
    }
    HorizontalDivider(
        color = colorScheme.borderMainSecondary,
        thickness = IBBorderDivider.borderDividerS,
    )
}

@Composable
private fun TableBody(
    exchangeRateList: List<ExchangeRateDMO>,
    limitedShownData: Int,
    modifier: Modifier = Modifier,
    firstColumnWeight: Float,
    secondColumnWeight: Float,
) {
    val colorScheme = LocalColorScheme.current
    exchangeRateList.take(limitedShownData).forEachIndexed { index, pair ->
        val shouldHideDivider =
            index == limitedShownData - 1 ||
                (limitedShownData < exchangeRateList.size && index == exchangeRateList.size - 1)

        Row(
            modifier =
                Modifier
                    .padding(IBSpacing.spacingS)
                    .then(modifier),
        ) {
            TableCellText(
                text = pair.jfxCod.orEmpty(),
                weight = firstColumnWeight,
                style = LocalTypography.current.bodyBody_m.copy(color = colorScheme.contentMainPrimary),
                textAlign = TextAlign.Start,
            )
            TableCellText(
                text = pair.bidBuy.orEmpty(),
                weight = secondColumnWeight,
                style = LocalTypography.current.titleTitle_s.copy(color = colorScheme.contentMainPrimary),
                textAlign = TextAlign.End,
                modifier = Modifier.padding(end = IBSpacing.spacingS),
            )
            TableCellText(
                text = pair.bidSel.orEmpty(),
                weight = 1 - firstColumnWeight - secondColumnWeight,
                style = LocalTypography.current.titleTitle_s.copy(color = colorScheme.contentMainPrimary),
                textAlign = TextAlign.End,
            )
        }

        if (!shouldHideDivider) {
            HorizontalDivider(
                color = LocalColorScheme.current.borderMainSecondary,
                thickness = IBBorderDivider.borderDividerS,
            )
        }
    }
}

@Composable
private fun RowScope.TableCellText(
    text: String,
    weight: Float,
    modifier: Modifier = Modifier,
    style: TextStyle,
    textAlign: TextAlign = TextAlign.Center,
) {
    Text(
        text = text,
        modifier =
            Modifier
                .weight(weight)
                .then(modifier),
        style = style,
        textAlign = textAlign,
    )
}
