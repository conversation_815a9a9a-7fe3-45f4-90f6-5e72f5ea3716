package vn.com.bidv.feature.homepage.ui.avatarpicker

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer.AvatarPickerViewEvent
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer.AvatarPickerViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.ui.BaseScreen
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.designsystem.component.datadisplay.avatar.AvatarInfo
import vn.com.bidv.designsystem.component.datadisplay.avatar.IBankAvatar
import vn.com.bidv.feature.common.ui.component.IBankImageCropper
import vn.com.bidv.feature.common.ui.component.LocalImagePicker
import vn.com.bidv.sdkbase.ui.component.snackbar.SnackBarPosition
import vn.com.bidv.sdkbase.ui.component.snackbar.pinSnackBarPosition

@Composable
fun AvatarPickerScreen(navController: NavHostController) {
    val vm: AvatarPickerViewModel = hiltViewModel()
    val context = LocalContext.current
    val (uiState, _, _) = vm.unpack()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(AvatarPickerViewEvent.OnInitData)
            }
            AvatarPickerContent(uiState, onEvent)
        },
        handleSideEffect = { sideEffect ->
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.cai_dat_hinh_dai_dien),
        )
    )
}

@Composable
fun AvatarPickerContent(
    uiState: AvatarPickerViewState,
    onEvent: (AvatarPickerViewEvent) -> Unit,
) {

    val avatarInfo = AvatarInfo(
        name = "",
        imageUrl = uiState.profileImageUrl
    )

    Box(
        modifier = Modifier
            .padding(IBSpacing.spacingM)
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = IBSpacing.spacing5xl),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            IBankAvatar(
                modifier = Modifier.size(268.dp),
                avatarInfo = avatarInfo,
                placeholderIcon = RDesignSystem.drawable.anonymous_avatar
            )
        }

        IBankImageCropper(
            onImageCropped = { uri ->
                onEvent(AvatarPickerViewEvent.OnChangeAvatar(uri))
            },
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
        ) {
            val launchPicker = LocalImagePicker.current
            IBankNormalButton(
                modifier = Modifier
                    .pinSnackBarPosition(
                        snackBarPosition = SnackBarPosition.BottomIn
                    )
                    .fillMaxWidth(),
                leadingIcon = ImageVector.vectorResource(RDesignSystem.drawable.edit_outline),
                text = stringResource(RLocalization.string.thay_doi)
            ) {
                launchPicker?.invoke()
            }
        }
    }
}

