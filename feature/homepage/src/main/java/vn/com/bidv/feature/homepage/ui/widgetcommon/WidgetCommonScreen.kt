package vn.com.bidv.feature.homepage.ui.widgetcommon

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.localization.R

@Composable
fun WidgetCommonScreen(
    headerView: @Composable () -> Unit = {},
    state: WidgetState,
    onClickRetry: () -> Unit,
    modifier: Modifier,
    message: String = stringResource(R.string.khong_tai_duoc_du_lieu),
    messageNoData: String = stringResource(R.string.khong_co_du_lieu),
    messageNoAccountCif: String = stringResource(R.string.khong_co_du_lieu),
    contentView: @Composable () -> Unit = {},
) {
    Box(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {
            headerView()
            when (state) {
                WidgetState.LOADING -> {
                    Box(modifier = Modifier
                        .heightIn(IBSpacing.spacing5xl)
                        .fillMaxWidth()) {
                        contentView()
                        LoadingViewWidget(
                            modifier = Modifier.matchParentSize()
                        )
                    }
                }

                WidgetState.CONTENT -> {
                    contentView()
                }

                WidgetState.NOCONTENT -> {
                    WidgetNoData(messageNoData = messageNoData)
                }

                WidgetState.ERROR -> {
                    WidgetErrorScreen(
                        onClickRetry = {
                            onClickRetry.invoke()
                        },
                        message = message
                    )
                }

                WidgetState.NO_ACCOUNT_CIF -> {
                    WidgetNoData(messageNoData = messageNoAccountCif)
                }
            }
        }
    }
}

@Composable
fun LoadingViewWidget(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        IBankLoaderIndicators(
            backgroundColor = LocalColorScheme.current.bgMainTertiary, isParentFullSize = true
        )
    }
}

@Composable
fun WidgetNoData(
    modifier: Modifier = Modifier,
    messageNoData: String = stringResource(R.string.khong_co_du_lieu),
) {
    IBankEmptyState(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = IBSpacing.spacingM),
        supportingText = messageNoData,
    )
}

@Composable
fun WidgetErrorScreen(
    modifier: Modifier = Modifier,
    message: String,
    onClickRetry: (() -> Unit) = {}
) {
    IBankEmptyState(
        modifier = modifier
            .fillMaxWidth(),
        supportingText = message,
        textButton = stringResource(R.string.thu_lai),
        leadingIconButton = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.refresh_outline),
        onClickButton = {
            onClickRetry()
        }
    )
}
