package vn.com.bidv.feature.homepage.ui.paymentaccountdetails

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.feature.common.domain.data.AccountDMO
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.FinAcctDetailDMO
import vn.com.bidv.feature.homepage.domain.model.AccountBalanceDMO

class PaymentAccountDetailsReducer :
    Reducer<PaymentAccountDetailsReducer.PaymentAccountDetailsViewState, PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent, PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect> {

    @Immutable
    data class PaymentAccountDetailsViewState(
        val isInitData: Boolean = false,
        val finAccDetail: FinAcctDetailDMO? = null,
        val state: WidgetState = WidgetState.LOADING,
        val bottomSheetState: SearchDialogState = SearchDialogState.LOADING,
        val isShowData: Boolean = false,
        val isShowBottomSheetAccount: Boolean = false,
        val listAccount: List<AccountDMO> = emptyList(),
        val currentAccount: AccountDMO? = null,
        val accountBalance: AccountBalanceDMO? = null,
        val messageError: String? = null,
        val isFailBalance: Boolean = false
    ) : ViewState

    @Immutable
    sealed class PaymentAccountDetailsViewEvent : ViewEvent {
        data object OnFetchData : PaymentAccountDetailsViewEvent()
        data class GetFinAcctDetailSuccess(val finAccDetail: FinAcctDetailDMO?) :
            PaymentAccountDetailsViewEvent()

        data class GetFinAcctDetailFail(val messageError: String?, val isFailBalance: Boolean) : PaymentAccountDetailsViewEvent()
        data object GetFinAcctDetailFailCif : PaymentAccountDetailsViewEvent()
        data class SetShowData(val isShowData: Boolean) : PaymentAccountDetailsViewEvent()
        data class SetShowBottomSheet( val isShowBottomSheetAccount: Boolean, val currentAccount: AccountDMO? = null) : PaymentAccountDetailsViewEvent()
        data class GetListAccountSuccess(val listAccount: List<AccountDMO>, val currentAccount: AccountDMO?) :
            PaymentAccountDetailsViewEvent()

        data object GetListAccountFail : PaymentAccountDetailsViewEvent()
        data object SetDefaultAccountSuccess : PaymentAccountDetailsViewEvent()
        data class GetAccountBalanceSuccess(val accountBalance: AccountBalanceDMO?) : PaymentAccountDetailsViewEvent()

    }

    @Immutable
    sealed class PaymentAccountDetailsViewEffect : SideEffect {
        data object GetFinAcctDetailViewEffect : PaymentAccountDetailsViewEffect()
        data class GetDataAccount(val finAccDetail: FinAcctDetailDMO?) : PaymentAccountDetailsViewEffect()
        data class UpdateAccountDefault(val currentAccount: AccountDMO?, val newAccount: AccountDMO?) : PaymentAccountDetailsViewEffect()
        data class GetAccountBalanceViewEffect(val accountNo: String?) : PaymentAccountDetailsViewEffect()
    }

    override fun reduce(
        previousState: PaymentAccountDetailsViewState,
        event: PaymentAccountDetailsViewEvent,
    ): Pair<PaymentAccountDetailsViewState, PaymentAccountDetailsViewEffect?> {
        return when (event) {
            is PaymentAccountDetailsViewEvent.OnFetchData -> {
                if (previousState.isInitData) {
                    if (previousState.isFailBalance) {
                        previousState.copy(
                            state = WidgetState.LOADING,
                            isFailBalance = false
                        ) to PaymentAccountDetailsViewEffect.GetAccountBalanceViewEffect(accountNo = previousState.finAccDetail?.accountNo)
                    } else {
                        previousState.copy(
                            state = WidgetState.LOADING
                        ) to PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect
                    }
                } else {
                    previousState.copy(
                        isInitData = true
                    ) to PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect
                }
            }

            is PaymentAccountDetailsViewEvent.GetFinAcctDetailFail -> {
                previousState.copy(
                    state = WidgetState.ERROR,
                    messageError = event.messageError,
                    isFailBalance = event.isFailBalance
                ) to null
            }

            is PaymentAccountDetailsViewEvent.GetFinAcctDetailSuccess -> {
                previousState.copy(
                    state = if (event.finAccDetail == null || event.finAccDetail.accountNo.isNullOrEmpty()) WidgetState.NOCONTENT else WidgetState.CONTENT,
                    finAccDetail = event.finAccDetail
                ) to null
            }

            is PaymentAccountDetailsViewEvent.SetShowData -> {
                if (previousState.accountBalance == null) {
                    previousState.copy(
                        state = WidgetState.LOADING,
                        isShowData = event.isShowData
                    ) to PaymentAccountDetailsViewEffect.GetAccountBalanceViewEffect(accountNo = previousState.finAccDetail?.accountNo)
                } else {
                    previousState.copy(
                        isShowData = event.isShowData
                    ) to null
                }
            }

            is PaymentAccountDetailsViewEvent.SetShowBottomSheet -> {
                if (event.isShowBottomSheetAccount) {
                    previousState.copy(
                        isShowBottomSheetAccount = event.isShowBottomSheetAccount,
                        bottomSheetState = SearchDialogState.LOADING
                    ) to PaymentAccountDetailsViewEffect.GetDataAccount(previousState.finAccDetail)
                } else {
                    previousState.copy(
                        isShowBottomSheetAccount = event.isShowBottomSheetAccount
                    ) to PaymentAccountDetailsViewEffect.UpdateAccountDefault(currentAccount = previousState.currentAccount, newAccount = event.currentAccount)
                }
            }

            is PaymentAccountDetailsViewEvent.GetListAccountFail -> {
                previousState.copy(
                    bottomSheetState = SearchDialogState.ERROR
                ) to null
            }

            is PaymentAccountDetailsViewEvent.GetListAccountSuccess -> {
                previousState.copy(
                    bottomSheetState = SearchDialogState.CONTENT,
                    listAccount = event.listAccount,
                    currentAccount = event.currentAccount
                ) to null
            }

            is PaymentAccountDetailsViewEvent.SetDefaultAccountSuccess -> {
                previousState.copy(
                    state = WidgetState.LOADING,
                    isShowData = false,
                    accountBalance = null
                ) to PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect
            }

            is PaymentAccountDetailsViewEvent.GetAccountBalanceSuccess -> {
                previousState.copy(
                    state = WidgetState.CONTENT,
                    accountBalance = event.accountBalance
                ) to null
            }

            is PaymentAccountDetailsViewEvent.GetFinAcctDetailFailCif -> {
                previousState.copy(
                    state = WidgetState.NO_ACCOUNT_CIF,
                ) to null
            }
        }

    }
}
