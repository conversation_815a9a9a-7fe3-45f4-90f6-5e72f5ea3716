package vn.com.bidv.feature.homepage.ui.home

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.constants.TypePermission
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.homepage.domain.HomeUseCase
import vn.com.bidv.feature.homepage.domain.OTTUseCase
import vn.com.bidv.feature.homepage.ui.home.HomeReducer.HomeViewEffect
import vn.com.bidv.feature.homepage.ui.home.HomeReducer.HomeViewEvent
import vn.com.bidv.feature.homepage.ui.home.HomeReducer.HomeViewState
import vn.com.bidv.feature.homepage.ui.service.modelUI.PermissionCode
import vn.com.bidv.feature.homepage.ui.service.modelUI.removeDefaultModel
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.LocaleManager
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val userInfoUseCase: UserInfoUseCase,
    private val homeUseCase: HomeUseCase,
    private val ottUseCase: OTTUseCase,
    private val authProvider: AuthProvider,
    @ApplicationContext private val context: Context
) : ViewModelIBankBase<HomeViewState, HomeViewEvent, HomeViewEffect>(
    initialState = HomeViewState(),
    reducer = HomeReducer()
) {
    override fun handleEffect(sideEffect: HomeViewEffect, onResult: (HomeViewEvent) -> Unit) {
        when(sideEffect) {
            is HomeViewEffect.GetWelcomeUserInfoFromStorage -> {
                val userInfo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user ?: UserResDMO()
                onResult(
                    HomeViewEvent.GetWelcomeUserInfoFromStorageSuccess(
                        userInfo = userInfo,
                        quickLinks = userInfoUseCase.getQuickLinks().removeDefaultModel().take(5) + PermissionResDMO(code = PermissionCode.CUSTOM_WIDGET.code, type = TypePermission.MENU.value),
                        widgets = userInfoUseCase.getWidgets()
                    )
                )
            }

            is HomeViewEffect.UpdateQuickLinkSideEffect -> {
                showSnackBar(
                    IBankSnackBarInfo(
                        message = resourceProvider.getString(R.string.luu_thanh_cong),
                        primaryButtonText = resourceProvider.getString(R.string.dong)
                    )
                )
                onResult(
                    HomeViewEvent.UpdateQuickLinkSuccess(
                        quickLinks = userInfoUseCase.getQuickLinks().removeDefaultModel().take(5) + PermissionResDMO(code = PermissionCode.CUSTOM_WIDGET.code, type = TypePermission.MENU.value)
                    )
                )
            }

            is HomeViewEffect.UpdateFCMIdSideEffect -> {
                updateFCMID {
                    onResult(HomeViewEvent.OnUpdateLoginStatusSuccess)
                }
            }

            is HomeViewEffect.UpdateLoginStatusSuccess -> {
                localRepository.setLoginStatus(isSuccess = true)
                onResult(HomeViewEvent.OnUpdateUserLanguage)
            }

            is HomeViewEffect.UpdateUserLanguage -> {
                updateUserLanguage()
            }

            is HomeViewEffect.UpdateWidgetSideEffect -> {
                showSnackBar(
                    IBankSnackBarInfo(
                        message = resourceProvider.getString(R.string.tuy_chinh_tien_ich_thanh_cong),
                        primaryButtonText = resourceProvider.getString(R.string.dong)
                    )
                )
                onResult(
                    HomeViewEvent.UpdateWidgetSuccess(
                        widgets = userInfoUseCase.getWidgets()
                    )
                )
            }

            is HomeViewEffect.Logout -> {
                callDomain(
                    isListenAllError = true,
                    isListenSessionExpired = true,
                    onSuccess = {
                        onResult(HomeViewEvent.OnLogoutSuccess)
                    },
                    onFail = {
                        onResult(HomeViewEvent.OnLogoutSuccess)
                    }
                ) {
                    authProvider.logout()
                }
            }

            is HomeViewEffect.UpdateAvatarSideEffect -> {
                val userInfo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user ?: UserResDMO()
                onResult(
                    HomeViewEvent.UpdateAvatarSuccess(
                        userInfo = userInfo,
                    )
                )
            }

            else -> {
                // do nothing
            }
        }
    }

    private fun updateUserLanguage() {
        val serverLanguage = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user?.langCode
        val clientLanguage = LocaleManager.getCurrentLanguage(context).apiDefLang
        callDomain(
            showLoadingIndicator = false,
            isListenAllError = true,
        ) {
            homeUseCase.updateUserLanguage(serverLanguage, clientLanguage)
        }
    }

    private fun updateFCMID(onResult: () -> Unit) {
        viewModelScope.launch {
            val fcmId = try {
                FirebaseMessaging.getInstance().token.await()
            } catch (e: Exception) {
                null
            }
            fcmId?.let {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onFail = {
                        // do nothing
                    },
                ) {
                    ottUseCase.registerFCMId(it)
                }

            }
            onResult()
        }
    }
}
