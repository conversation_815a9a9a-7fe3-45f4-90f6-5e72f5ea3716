package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import javax.inject.Inject

class OTTUseCase @Inject constructor(
    private val notificationRepository: UtilitiesRepository,
) {

    suspend fun registerFCMId(fcmId: String): DomainResult<Boolean> {
        val networkResult = notificationRepository.registerFCMId(fcmId)
        if (networkResult is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = networkResult.errorCode,
                errorMessage = networkResult.errorMessage
            )
        }

        return DomainResult.Success(true)
    }
}