package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.common.data.MasterDataRepository
import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.data.masterdata.model.AuthorizedAccountListCriteriaDto
import vn.com.bidv.feature.common.domain.data.AccGroupType
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.domain.model.AccountBalanceDMO
import vn.com.bidv.feature.homepage.domain.model.DataListAccountDMO
import vn.com.bidv.feature.homepage.domain.model.FinAcctDetailDMO
import vn.com.bidv.network.retrofit.NetworkResponse
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.mapTo
import javax.inject.Inject

class PaymentAccountDetailsUseCase @Inject constructor(
    private val homepageRepository: HomepageRepository,
    private val masterRepository: MasterDataRepository,
    private val utilitiesRepository: UtilitiesRepository,
) {
    suspend fun getAccountDetail(): DomainResult<FinAcctDetailDMO> {
        val domain = homepageRepository.getAccountDetail()
        val result = domain.convert(FinAcctDetailDMO::class.java)
        return result
    }

    suspend fun getAccountBalance(accountNo: String?): DomainResult<AccountBalanceDMO> {
        val domain = homepageRepository.getAccountBalance(accountNo = accountNo)
        val result = domain.convert(AccountBalanceDMO::class.java)
        return result
    }

    suspend fun getAuthorizedAccountList(): DomainResult<DataListAccountDMO> {
        val domain = masterRepository.getAuthorizedAccountList(
            AuthorizedAccountListCriteriaDto(
                accType = AccGroupType.DDA.code,
                grpType = AccGroupType.ACC_INQ.code
            )
        )
        val result = domain.convert(DataListAccountDMO::class.java)
        return result
    }

    suspend fun setDefaultAccount(accNo: String) : DomainResult<Boolean> {
        val domain = utilitiesRepository.setDefaultAccount(
            accType = AccGroupType.DDA.code,
            accNo = accNo,
            grpType = AccGroupType.ACC_INQ.code,
            enable = true
        )
        return domain.convert {
            this.mapTo(NetworkResponse::class.java).isSuccess()
        }
    }

}