package vn.com.bidv.feature.homepage.ui.homepage

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class HomePageReducer :
    Reducer<HomePageReducer.HomePageViewState, HomePageReducer.HomePageViewEvent, HomePageReducer.HomePageViewEffect> {

    data class HomePageViewState(
        val selectedIndex: Int = 0,
        val shouldFetchTotalNotifyUnread: Boolean = true,
        val isShowQr: Boolean = true
    ) : ViewState

    @Immutable
    sealed class HomePageViewEvent : ViewEvent {
        data object CheckShowQrEvent : HomePageViewEvent()
        data class OnClickItemBottomBar(val index: Int) : HomePageViewEvent()
        data class MarkTotalNotifyAsShouldFetch(val shouldFetch: Boolean) : HomePageViewEvent()
        data class GetIsShowQr(val isShowQr: Boolean): HomePageViewEvent()
    }

    @Immutable
    sealed class HomePageViewEffect : SideEffect {
        data object CheckShowQrEffect : HomePageViewEffect()
    }

    override fun reduce(
        previousState: HomePageViewState,
        event: HomePageViewEvent,
    ): Pair<HomePageViewState, HomePageViewEffect?> {
        return when (event) {
            is HomePageViewEvent.OnClickItemBottomBar ->
                if (event.index != previousState.selectedIndex) {
                    previousState.copy(
                        selectedIndex = event.index
                    ) to null
                } else {
                    previousState to null
                }

            is HomePageViewEvent.MarkTotalNotifyAsShouldFetch ->
                previousState.copy(
                    shouldFetchTotalNotifyUnread = event.shouldFetch
                ) to null

            is HomePageViewEvent.CheckShowQrEvent ->
                previousState to HomePageViewEffect.CheckShowQrEffect

            is HomePageViewEvent.GetIsShowQr ->
                previousState.copy(isShowQr = event.isShowQr) to null

            else -> previousState to null
        }
    }
}
