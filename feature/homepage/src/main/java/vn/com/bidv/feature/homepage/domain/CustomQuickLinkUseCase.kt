package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_DATA_HOME
import vn.com.bidv.feature.homepage.constants.HomePageConstants.RELOAD_QUICK_LINK
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.feature.homepage.ui.service.modelUI.removeDefaultModel
import vn.com.bidv.feature.homepage.ui.service.modelUI.toPresenterModel
import vn.com.bidv.feature.homepage.utils.removeSpecialCharacters
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.ResourceProvider
import vn.com.bidv.sdkbase.utils.VNCharacterUtil
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class CustomQuickLinkUseCase @Inject constructor(
    private val resourceProvider: ResourceProvider,
    private val homepageRepository: HomepageRepository,
    private val localRepository: LocalRepository
) {
    fun getSearchListChildrenService(
        listChildrenService: List<ModelCheckAble<PermissionResDMO>>,
        keyWorks: String
    ): List<ModelCheckAble<PermissionResDMO>> {
        if (keyWorks.isEmpty()) return listChildrenService.filter { it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }

        return listChildrenService.filter { VNCharacterUtil.removeAccent(resourceProvider.getString(it.data.toPresenterModel().title)).contains(VNCharacterUtil.removeAccent(keyWorks).removeSpecialCharacters().trim()
            , ignoreCase = true) &&  it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink
        }
    }

    fun getListChildrenService(listChildrenService: List<PermissionResDMO>, listQuickLink: List<PermissionResDMO>): List<ModelCheckAble<PermissionResDMO>> {
        val result = mutableListOf<PermissionResDMO>()
        for (item in listChildrenService) {
            item.children?.let {
                result.addAll(it)
            } ?: emptyList<PermissionResDMO>()
        }


        return result.removeDefaultModel().filter { it.priorityLevel in HomePageConstants.validPriorityQuickLink }.map { ModelCheckAble(data = it, isChecked = listQuickLink.map { itQL -> itQL.code }.contains(it.code)) }
    }

    fun selectItemListChildrenService(listChildrenService: List<ModelCheckAble<PermissionResDMO>>, item: ModelCheckAble<PermissionResDMO>): List<ModelCheckAble<PermissionResDMO>> {
        val newListItems = listChildrenService.map {
            if (it.data.code == item.data.code) {
                it.copy(isChecked = !it.isChecked)
            } else {
                it
            }
        }

        return newListItems
    }

    fun selectItemListSearchChildrenService(listSearchChildrenService: List<ModelCheckAble<PermissionResDMO>>, item: ModelCheckAble<PermissionResDMO>): List<ModelCheckAble<PermissionResDMO>> {
        val newListItems = listSearchChildrenService.map {
            if (it.data.code == item.data.code) {
                it.copy(isChecked = !it.isChecked)
            } else {
                it
            }
        }

        newListItems.filter {  it.data.priorityLevel in HomePageConstants.validPriorityNormalQuickLink }

        return newListItems
    }

    fun selectItemQuickLinkService(
        listQuickLink: List<PermissionResDMO>,
        item: ModelCheckAble<PermissionResDMO>
    ): List<PermissionResDMO> {
        val mutableList = listQuickLink.toMutableList()

        if (item.isChecked) {
            mutableList.removeAll { it.code == item.data.code }
        } else {
            if (!mutableList.any { it.code == item.data.code }) {
                mutableList.add(item.data)
            }
        }

        return mutableList.toList()
    }

    suspend fun updateUserQuickLink(listQuickLink: List<String>) : DomainResult<String> {
        val domain = homepageRepository.updateUserQuickLink(listQuickLink)
        val result = domain.convert {
            this.message.orEmpty()
        }
        return result
    }

    suspend fun needReloadDataHome() {
        localRepository.shareDataTo(
            RELOAD_DATA_HOME, ShareDataDTO(
                RELOAD_DATA_HOME,
                RELOAD_QUICK_LINK)
            )
    }
}