package vn.com.bidv.feature.homepage.ui.paymentaccountbalance

import IBGradient
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.common.enum.CurrencyCode
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.BalanceCurrDetailDMO
import vn.com.bidv.feature.homepage.ui.paymentaccountbalance.component.AccountBalanceCardItem
import vn.com.bidv.feature.homepage.ui.widgetcommon.WidgetCommonScreen
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.formatMoney
import java.math.BigDecimal
import vn.com.bidv.designsystem.R as DesignSystemR

@Composable
fun PaymentAccountBalanceScreen(navController: NavController) {
    val viewModel = hiltViewModel<PaymentAccountBalanceViewModel>()

    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->

            LaunchedEffect(Unit) {
                if (!uiState.isInitialized) {
                    viewModel.sendEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.FetchUserInfo)
                }
            }

            PaymentAccountBalanceContent(
                uiState = uiState,
                onEvent = onEvent,
                modifier = Modifier,
            )
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is PaymentAccountBalanceReducer.PaymentAccountBalanceViewEffect.OnClickSeeMore -> {
                    navController.navigate(IBankMainRouting.InquiryRoute.PaymentAccountList.route)
                }

                else -> {}
            }
        },
    )
}

@Composable
private fun PaymentAccountBalanceContent(
    uiState: PaymentAccountBalanceReducer.PaymentAccountBalanceViewState,
    onEvent: (PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent) -> Unit = {},
    modifier: Modifier,
) {
    val colorSchema = LocalColorScheme.current
    Box(
        modifier =
            modifier
                .padding(IBSpacing.spacingM)
                .clip(shape = RoundedCornerShape(IBSpacing.spacingS))
                .background(color = colorSchema.bgMainTertiary),
    ) {
        WidgetCommonScreen(
            state = uiState.state,
            modifier = Modifier.heightIn(min = uiState.contentContainerHeight),
            message =
                uiState.errorMessage.ifEmpty {
                    stringResource(R.string.khong_tai_duoc_du_lieu)
                },
            messageNoAccountCif =
                stringResource(
                    R.string.khong_co_phan_quyen_dich_vu,
                ),
            headerView = {
                AccountBalanceHeader(
                    isBalanceHidden = uiState.shouldHideBalance,
                    isDataFetchedAtLeastOnce = uiState.dataFetchedAtLeastOnce,
                    onClickHideBalance = {
                        onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.OnToggleShowBalanceButton)
                    },
                    onClickRefreshData = { onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.OnRefreshData) },
                    onClickInfoIcon = {
                        onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.OnToggleShowBottomSheet)
                    },
                )
            },
            contentView = {
                Column {
                    AccountBalanceList(
                        balanceList = uiState.accountBalanceList,
                        shouldHideBalanceInfo = uiState.shouldHideBalance,
                        hasDataFetchedOnce = uiState.dataFetchedAtLeastOnce,
                        cardItemHeight = uiState.cardItemHeight,
                        cardItemWidth = uiState.cardItemWidth,
                        onClickSeeMore = { onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.OnClickSeeMore) },
                    )
                    AccountBalanceUpdatedTime(
                        updatedTime = uiState.updatedDate,
                        hasDataFetchedOnce = uiState.dataFetchedAtLeastOnce,
                    )
                }
            },
            onClickRetry = {
                onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.GetAccountBalanceList)
            },
        )
    }
    if (uiState.shouldShowBottomSheet) {
        BottomSheetInfomation(
            title = stringResource(R.string.so_du_thanh_toan_kha_dung),
            content = "*${stringResource(R.string.theo_phan_quyen_dang_ky)}",
            onDismiss = {
                onEvent(PaymentAccountBalanceReducer.PaymentAccountBalanceViewEvent.OnToggleShowBottomSheet)
            },
        )
    }
}

@Composable
private fun AccountBalanceHeader(
    modifier: Modifier = Modifier,
    onClickInfoIcon: () -> Unit = {},
    onClickHideBalance: () -> Unit = {},
    onClickRefreshData: () -> Unit = {},
    isBalanceHidden: Boolean = false,
    isDataFetchedAtLeastOnce: Boolean = false,
) {
    val typography = LocalTypography.current
    val colorSchema = LocalColorScheme.current
    val iconSizeM = 16.dp
    val iconSizeL = 20.dp

    Row(
        modifier =
            modifier
                .padding(top = IBSpacing.spacingS, bottom = IBSpacing.spacingXs)
                .padding(horizontal = IBSpacing.spacingM),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = stringResource(R.string.so_du_thanh_toan_kha_dung),
            style = typography.titleTitle_m,
        )
        Spacer(modifier = Modifier.size(IBSpacing.spacing2xs))
        Icon(
            painter = painterResource(id = DesignSystemR.drawable.information_circle_outline),
            contentDescription = null,
            modifier =
                Modifier
                    .size(iconSizeM)
                    .clickable {
                        onClickInfoIcon()
                    },
            tint = colorSchema.contentMainPrimary,
        )
        Spacer(modifier = Modifier.weight(1f))
        Icon(
            painter =
                painterResource(
                    id =
                        if (!isBalanceHidden &&
                            isDataFetchedAtLeastOnce
                        ) {
                            DesignSystemR.drawable.eyes_closed_outline
                        } else {
                            DesignSystemR.drawable.eyes_open_outline
                        },
                ),
            contentDescription = null,
            modifier =
                Modifier
                    .size(iconSizeL)
                    .clickable {
                        onClickHideBalance()
                    },
            tint = colorSchema.contentMainPrimary,
        )
        Spacer(modifier = Modifier.size(IBSpacing.spacingL))
        Icon(
            painter = painterResource(id = DesignSystemR.drawable.refresh_outline),
            contentDescription = null,
            modifier =
                Modifier
                    .size(iconSizeL)
                    .clickable {
                        onClickRefreshData()
                    },
            tint = colorSchema.contentMainPrimary,
        )
    }
}

@Composable
fun AccountBalanceList(
    balanceList: List<BalanceCurrDetailDMO>,
    modifier: Modifier = Modifier,
    shouldHideBalanceInfo: Boolean = false,
    hasDataFetchedOnce: Boolean = false,
    cardItemWidth: Dp = 0.dp,
    cardItemHeight: Dp = 0.dp,
    onClickSeeMore: () -> Unit = {},
) {
    val listState = rememberLazyListState()
    val limitedShownItems = 4

    Box(
        modifier = modifier.fillMaxWidth(),
    ) {
        LazyRow(
            modifier = Modifier,
            state = listState,
            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            itemsIndexed(balanceList.take(limitedShownItems)) { index, item ->
                val isFirstVisible = listState.firstVisibleItemIndex == 0
                val isLastVisible =
                    listState.layoutInfo.visibleItemsInfo
                        .lastOrNull()
                        ?.index == balanceList.lastIndex

                val startPadding = if (index == 0 && isFirstVisible) IBSpacing.spacingM else 0.dp
                val endPadding =
                    if (index == balanceList.lastIndex && isLastVisible) IBSpacing.spacingM else 0.dp

                val currCode = CurrencyCode.entries.find { it.currencyCode == item.currCode }
                currCode?.let {
                    AccountBalanceCardItem(
                        currencyCode = it,
                        balance =
                            "${item.totalBalance}".formatMoney(
                                it.currencyCode,
                                isShowCurrCode = false,
                            ),
                        modifier = Modifier.padding(start = startPadding, end = endPadding),
                        shouldHideBalanceInfo = shouldHideBalanceInfo,
                        hasDataFetchedOnce = hasDataFetchedOnce,
                        cardItemWidth = cardItemWidth,
                        cardItemHeight = cardItemHeight,
                    )
                }
            }
            item {
                SeeMoreButton(
                    modifier =
                        Modifier
                            .padding(
                                start = IBSpacing.spacing2xl,
                                end = IBSpacing.spacing3xl,
                            ).clickable {
                                onClickSeeMore()
                            },
                )
            }
        }
        // Gradient and blur effect for last row item
        Box(
            modifier =
                Modifier
                    .width(IBSpacing.spacing5xl)
                    .heightIn(min = cardItemHeight)
                    .zIndex(2f)
                    .align(Alignment.CenterEnd)
                    .background(
                        brush =
                            IBGradient.tertiaryLeft_right,
                    ),
        )
    }
}

@Composable
fun AccountBalanceUpdatedTime(
    modifier: Modifier = Modifier,
    updatedTime: String,
    hasDataFetchedOnce: Boolean = false,
) {
    val typography = LocalTypography.current
    val colorSchema = LocalColorScheme.current
    Row(
        modifier =
            modifier
                .padding(vertical = IBSpacing.spacingM)
                .padding(horizontal = IBSpacing.spacingM),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text =
                stringResource(
                    R.string.cap_nhat_s,
                    if (!hasDataFetchedOnce) HomePageConstants.NOT_SHOW_DATE else updatedTime,
                ),
            style = typography.bodyBody_m,
            color = colorSchema.contentMainTertiary,
        )
    }
}

@Composable
fun SeeMoreButton(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Icon(
            painter = painterResource(id = DesignSystemR.drawable.circle_arrow_right_outline),
            contentDescription = null,
            modifier = Modifier.size(IBSpacing.spacingM),
            tint = LocalColorScheme.current.contentMainPrimary,
        )
        Spacer(modifier = Modifier.size(IBSpacing.spacing2xs))
        Text(
            text = stringResource(R.string.xem_them),
            style = LocalTypography.current.bodyBody_m,
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BottomSheetInfomation(
    title: String,
    content: String,
    onDismiss: (() -> Unit)? = {},
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    IBankBottomSheet(
        title = title,
        onDismiss = onDismiss,
        applyMinHeight = false
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth(),
        ) {
            HorizontalDivider(
                modifier = Modifier.padding(bottom = IBSpacing.spacingL),
                color = colorSchema.borderMainSecondary,
                thickness = IBBorderDivider.borderDividerS,
            )
            Text(
                text = content,
                color = colorSchema.contentMainPrimary,
                style = typography.bodyBody_l,
                textAlign = TextAlign.Start,
                modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingL))
        }
    }
}

@Preview
@Composable
fun BottomSheetInfomationPreview() {
    BottomSheetInfomation(
        title = "Title",
        content = "Content",
    )
}

@Preview
@Composable
fun PaymentAccountBalanceScreenPreview() {
    val uiState = PaymentAccountBalanceReducer.PaymentAccountBalanceViewState()
    PaymentAccountBalanceContent(modifier = Modifier, uiState = uiState)
}

@Preview
@Composable
fun PaymentAccountBalanceScreenErrorPreview() {
    val uiState =
        PaymentAccountBalanceReducer.PaymentAccountBalanceViewState(state = WidgetState.ERROR)
    PaymentAccountBalanceContent(modifier = Modifier, uiState = uiState)
}

@Preview
@Composable
fun PaymentAccountBalanceScreenNoDataPreview() {
    val uiState =
        PaymentAccountBalanceReducer.PaymentAccountBalanceViewState(state = WidgetState.LOADING)
    PaymentAccountBalanceContent(modifier = Modifier, uiState = uiState)
}

@Preview(showBackground = true)
@Composable
fun SeeMoreButtonPreview() {
    SeeMoreButton()
}
