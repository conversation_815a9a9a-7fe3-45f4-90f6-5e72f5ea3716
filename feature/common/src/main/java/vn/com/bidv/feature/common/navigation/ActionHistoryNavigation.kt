package vn.com.bidv.feature.common.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navigation
import vn.com.bidv.feature.common.ui.screen.actionhistory.ActionHistoryScreen
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import javax.inject.Inject

sealed class ActionHistoryRoute(val route: String) {
    data object ActionHistoryRouteMain : ActionHistoryRoute("action_history_main")
}

class ActionHistoryNavigation @Inject constructor() : FeatureGraphBuilder {
    override fun buildGraph(
        navGraphBuilder: NavGraphBuilder,
        navController: NavHostController,
        registeredRoutes: (args: List<String>) -> Unit
    ) {
        navGraphBuilder.navigation(
            startDestination = ActionHistoryRoute.ActionHistoryRouteMain.route,
            route = IBankMainRouting.CommonRoute.CommonActionHistoryRoute.routeWithArgument
        ) {
            composable(
                route = ActionHistoryRoute.ActionHistoryRouteMain.route,
                arguments = listOf(
                    navArgument(IBankMainRouting.CommonRoute.TXN_ID) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(IBankMainRouting.CommonRoute.TXN_CODE) {
                        type = NavType.StringType
                        nullable = true
                    }
                )
            ) { backStackEntry ->
                val txnId = backStackEntry.arguments?.getString(IBankMainRouting.CommonRoute.TXN_ID) ?: ""
                val txnCode = backStackEntry.arguments?.getString(IBankMainRouting.CommonRoute.TXN_CODE) ?: ""
                ActionHistoryScreen(
                    navController = navController,
                    txnId = txnId,
                    txnCode = txnCode
                )
            }
        }
        registeredRoutes(listOf(IBankMainRouting.CommonRoute.CommonActionHistoryRoute.route))
    }
}