package vn.com.bidv.feature.common.ui.screen.commonlisttransaction

import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer.ListTransactionBaseViewEffect
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer.ListTransactionBaseViewEvent
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer.ListTransactionBaseViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

abstract class ListTransactionBaseViewModel<T, R>(
    val initialState: ListTransactionBaseViewState<T, R>,
    val reducer: ListTransactionBaseReducer<T, R>,
) : ViewModelIBankBase<ListTransactionBaseViewState<T, R>, ListTransactionBaseViewEvent<T, R>, ListTransactionBaseViewEffect<T>>(
    initialState = initialState,
    reducer = reducer
) {

    override fun handleEffect(
        sideEffect: ListTransactionBaseViewEffect<T>,
        onResult: (ListTransactionBaseViewEvent<T, R>) -> Unit
    ) {

    }
}
