package vn.com.bidv.feature.common.ui.screen.actionhistory

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.GetActionHistoryUseCase
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ActionHistoryViewModel @Inject constructor(
    private val getActionHistoryUsecase: GetActionHistoryUseCase,
) : ViewModelIBankBase<ActionHistoryReducer.ActionHistoryState, ActionHistoryReducer.ActionHistoryEvent, ActionHistoryReducer.ActionHistoryEffect>(
    initialState = ActionHistoryReducer.ActionHistoryState(), reducer = ActionHistoryReducer()
) {
    override fun handleEffect(
        sideEffect: ActionHistoryReducer.ActionHistoryEffect,
        onResult: (ActionHistoryReducer.ActionHistoryEvent) -> Unit
    ) {
        super.handleEffect(sideEffect, onResult)
        when (sideEffect) {
            is ActionHistoryReducer.ActionHistoryEffect.GetActionLogsSideEffect -> callDomain(
                isListenAllError = true,
                onSuccess = {
                    onResult(
                        ActionHistoryReducer.ActionHistoryEvent.GetActionLogsSuccess(
                            actionLogs = it.data?.items
                        )
                    )
                },
                onFail = {
                    onResult(
                        ActionHistoryReducer.ActionHistoryEvent.ShowError(
                            errorMessage = it?.errorMessage ?: ""
                        )
                    )
                }) {
                getActionHistoryUsecase.invoke(
                    txnId = sideEffect.txnId,
                    txnCode = sideEffect.txnCode
                )
            }
        }
    }

}