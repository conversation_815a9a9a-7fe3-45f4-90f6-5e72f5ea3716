package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.VerifyMethodType

class VerifyOtpReducer :
    Reducer<VerifyOtpReducer.VerifyOtpViewState, VerifyOtpReducer.VerifyOtpViewEvent, VerifyOtpReducer.VerifyOtpViewEffect> {

    @Immutable
    data class VerifyOtpViewState(
        val isInitSuccess: Boolean = false,
        val modelTransaction: ModelVerifyTransactionUI = ModelVerifyTransactionUI(),
        val listOtp: List<String> = emptyList(),
        val currentOtp: String = "",
        val secretKey: String = "",
        val smToken: String = "",
        val isLoginSuccess: Boolean = false
    ) : ViewState

    @Immutable
    sealed class VerifyOtpViewEvent : ViewEvent {
        data class OnInitTransaction(
            val modelTransaction: ModelVerifyTransactionUI,
            val secretKey: String,
            val smToken: String
        ) :
            VerifyOtpViewEvent()
        data class OnInitTransactionSuccess(val isLoginSuccess: Boolean): VerifyOtpViewEvent()

        data class OnGetListOtpCodeSuccess(val listOtp: List<String>) : VerifyOtpViewEvent()
        data object OnConfirmVerifyOtpTransaction : VerifyOtpViewEvent()
        data class OnShareOtpTransactionSuccess(val otpVerify: String) : VerifyOtpViewEvent()
        data object OnGetNextOtpCode : VerifyOtpViewEvent()
    }

    @Immutable
    sealed class VerifyOtpViewEffect : SideEffect {
        data object InitTransaction: VerifyOtpViewEffect()
        data class GetListOtpCode(val authId: String, val secretKey: String) : VerifyOtpViewEffect()
        data class ConfirmVerifyOtpTransaction(
            val authId: String,
            val otp: String,
            val verifyMethodType: VerifyMethodType,
            val smToken: String
        ) : VerifyOtpViewEffect()

        data class ShareOtpTransactionSuccess(val otpVerify: String) : VerifyOtpViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: VerifyOtpViewState,
        event: VerifyOtpViewEvent,
    ): Pair<VerifyOtpViewState, VerifyOtpViewEffect?> {
        return handleVerifyOtpViewState(previousState, event)
    }

    private fun handleVerifyOtpViewState(
        previousState: VerifyOtpViewState,
        event: VerifyOtpViewEvent
    ): Pair<VerifyOtpViewState, VerifyOtpViewEffect?> {
        return when (event) {
            is VerifyOtpViewEvent.OnInitTransaction -> {
                previousState.copy(
                    isInitSuccess = true,
                    modelTransaction = event.modelTransaction,
                    secretKey = event.secretKey,
                    smToken = event.smToken
                ) to VerifyOtpViewEffect.InitTransaction
            }

            is VerifyOtpViewEvent.OnInitTransactionSuccess -> {
                previousState.copy(isLoginSuccess = event.isLoginSuccess) to VerifyOtpViewEffect.GetListOtpCode(
                    previousState.modelTransaction.authId,
                    previousState.secretKey
                )
            }

            is VerifyOtpViewEvent.OnConfirmVerifyOtpTransaction -> {
                previousState to VerifyOtpViewEffect.ConfirmVerifyOtpTransaction(
                    authId = previousState.modelTransaction.authId,
                    otp = previousState.currentOtp,
                    verifyMethodType = previousState.modelTransaction.verifyMethodType
                        ?: VerifyMethodType.SAME_DEVICE,
                    smToken = previousState.smToken
                )
            }

            is VerifyOtpViewEvent.OnShareOtpTransactionSuccess -> {
                previousState to VerifyOtpViewEffect.ShareOtpTransactionSuccess(event.otpVerify)
            }

            is VerifyOtpViewEvent.OnGetListOtpCodeSuccess -> {
                previousState.copy(
                    listOtp = event.listOtp,
                    currentOtp = getNextOtpCode(previousState.currentOtp, event.listOtp),
                ) to null
            }

            is VerifyOtpViewEvent.OnGetNextOtpCode -> {
                if (isCheckLatestOtp(previousState.currentOtp, previousState.listOtp)) {
                    previousState to null
                } else {
                    previousState.copy(
                        currentOtp = getNextOtpCode(
                            previousState.currentOtp,
                            previousState.listOtp
                        ),
                    ) to null
                }
            }

        }
    }

    private fun isCheckLatestOtp(currentOtp: String, listOtp: List<String>): Boolean {
        return listOtp.indexOf(currentOtp) == listOtp.size - 1
    }

    private fun getNextOtpCode(currentOtp: String, listOtp: List<String>): String {
        return if (currentOtp.isEmpty()) {
            listOtp.firstOrNull() ?: ""
        } else {
            val index = listOtp.indexOf(currentOtp)
            if (index < listOtp.size - 1) {
                listOtp[index + 1]
            } else {
                listOtp.lastOrNull() ?: ""
            }
        }
    }
}
