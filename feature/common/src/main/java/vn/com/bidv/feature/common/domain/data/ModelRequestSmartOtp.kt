package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
@Serializable
data class ModelRequestSmartOtp(
    @SerializedName("smartOtp")
    val smartOtp: SmartOtpDMO? = null,
    @SerializedName("userActiveSmartOtpDMO")
    val userActiveSmartOtpDMO: UserActiveSmartOtpDMO? = null,
    @SerializedName("status")
    val status: SmartOtpStatus = SmartOtpStatus.ACTIVE,
)

enum class SmartOtpStatus {
    ACTIVE,
    CHANGE_PIN
}

