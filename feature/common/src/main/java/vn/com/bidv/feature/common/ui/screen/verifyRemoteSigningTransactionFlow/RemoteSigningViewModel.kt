package vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.domain.RemoteSigningUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow.RemoteSigningReducer.RemoteSigningViewEffect
import vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow.RemoteSigningReducer.RemoteSigningViewEvent
import vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow.RemoteSigningReducer.RemoteSigningViewState
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class RemoteSigningViewModel @Inject constructor(
    private val remoteSigningUseCase: RemoteSigningUseCase,
    val itemVerifyFlowScreenBuilder: Set<@JvmSuppressWildcards VerifyTransactionFlowScreenBuilder>,
    ) : ViewModelIBankBase<RemoteSigningViewState, RemoteSigningViewEvent, RemoteSigningViewEffect>(
    initialState = RemoteSigningViewState(),
    reducer = RemoteSigningReducer()
) {
    private var job: CompletableJob? = null
    private var scope: CoroutineScope? = null

    override fun handleEffect(
        sideEffect: RemoteSigningViewEffect,
        onResult: (RemoteSigningViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is RemoteSigningViewEffect.DoInitRemoteSigningEffect -> {
                startJobReceiverOtp(sideEffect.authId, onResult)
            }

            else -> {
                //Do Nothing
            }
        }
    }

    private fun startJobReceiverOtp(authId: String, onResult: (RemoteSigningViewEvent) -> Unit) {
        if (job?.isActive == true) {
            return
        }
        job = SupervisorJob(viewModelScope.coroutineContext[Job])
        job?.let { job ->
            scope = CoroutineScope(Dispatchers.IO + job)
            scope?.launch {
                while (true) {
                    delay(5000)
                    when (val result = remoteSigningUseCase.retrieveAuthStatus(authId)) {
                        is DomainResult.Success -> {
                            cancelJobReceiverOtp()
                            onResult(RemoteSigningViewEvent.DoInitRemoteSigningSuccess(result.data))
                        }

                        is DomainResult.Error -> {
                            // do thing
                            BLogUtil.d("")
                        }
                    }
                }
            }
        }
    }

    private fun cancelJobReceiverOtp() {
        if (job?.isActive == true && job?.isCompleted == false) {
            job?.cancel()
        }
    }
}
