package vn.com.bidv.feature.common.domain.smartotp

import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO

interface ISmartOTPUseCase {
    fun getListUserSmartOtpDMO(): List<UserSmartOtpDMO>
    fun saveUserActiveSmartOtpDMO(newItem: UserActiveSmartOtpDMO)
    fun verifyPin(pin: String, userId: String): UserActiveSmartOtpDMO?
    fun checkPinBlocked(userId: String): Boolean
    fun deleteUserSmartOtp(userId: String)
    fun deleteAllUserSmartOtp()
}