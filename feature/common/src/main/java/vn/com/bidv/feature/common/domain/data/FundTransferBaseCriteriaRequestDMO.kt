package vn.com.bidv.feature.common.domain.data

import vn.com.bidv.feature.common.data.foreport.model.Filter
import vn.com.bidv.feature.common.data.foreport.model.Order
import vn.com.bidv.feature.common.data.foreport.model.Page

data class FundTransferBaseCriteriaRequestDMO(
    val orders: List<Order>? = null,

    val filters: List<Filter>? = null,

    val page: Page? = null,

    /* Using keyword for filter by some fields */
    val keyword: String? = null,

    /* Filter by createDate greater than or equal to input value */
    val startDate: String? = null,

    /* Filter by createDate less than or equal to input value */
    val endDate: String? = null,

    /* Filter by effDate greater than or equal to input value */
    val startEffDate: String? = null,

    /* Filter by effDate less than or equal to input value */
    val endEffDate: String? = null,

    /* Filter by debitAccNo */
    val debitAccNo: String? = null,

    /* Filter by ben ident/card/acc No */
    val benValue: String? = null,

    /* Filter by amountMin */
    val amountMin: String? = null,

    /* Filter by amountMax */
    val amountMax: String? = null,

    /* Filter batch No */
    val batchNo: String? = null,

    /* Filter by channel */
    val channel: List<String>? = null,

    /* Filter by transaction currency */
    val ccy: String? = null,

    /* Filter by benBankCode */
    val benBankCode: List<String>? = null,

    /* Filter by txnType */
    val txnType: List<String>? = null,

    /* Filter by status, leave empty to get all */
    val status: List<String>? = null,

    /* Filter by core ref, leave empty to get all */
    val coreRef: String? = null
)