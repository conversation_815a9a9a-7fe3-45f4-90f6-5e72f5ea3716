package vn.com.bidv.feature.common.ui.screen.commonlisttransaction

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import kotlinx.coroutines.flow.distinctUntilChanged
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer.ListTransactionBaseViewEvent
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.button.IBankBadgeButton
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMore
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import vn.com.bidv.feature.common.constants.Constants.MAX_LENGTH_LIST_TRANSACTION
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ListTransactionRuleFilter
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.TypeBottomAction
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.ui.component.snackbar.SnackBarPosition
import vn.com.bidv.sdkbase.ui.component.snackbar.pinSnackBarPosition

@Composable
fun <T, R : ListTransactionRuleFilter> ListTransactionBaseScreen(
    viewModel: ListTransactionBaseViewModel<T, R>,
    navController: NavHostController,
    lazyListState: LazyListState,
    loadListViewModel: ListAutoLoadMoreViewModel<T, R>,
    hintTextSearch: String = stringResource(vn.com.bidv.localization.R.string.tim_kiem),
    textEmptySearch: String = stringResource(vn.com.bidv.localization.R.string.khong_ton_tai_giao_dich_theo_dieu_kien_tim_kiem),
    resIdTextTransactionType: Int = vn.com.bidv.localization.R.string.d_hoa_don,
    isHaveCheckBoxSelectAll: Boolean = true,
    advSearchContent: @Composable (R) -> Unit = {},
    avdSearchClearTitle: String = stringResource(vn.com.bidv.localization.R.string.xoa_bo_loc),
    listAction: List<ActionType> = emptyList(),
    handleAction: (ActionType, List<ModelCheckAble<T>>) -> Unit = { _, _ -> },
    ruleFilterDefault: R,
    rightContent: @Composable ((R) -> Unit)? = null,
    reloadKey: ReloadKey? = null,
    shouldShowSearchArea: Boolean = true,
    shouldShowCreateButton: Boolean = true,
    itemContent: @Composable (
        ModelCheckAble<T>,
        ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, R>) -> Unit)?,
    ) -> Unit = { _, _ -> },
) {
    var showAdvSearch by remember { mutableStateOf(false) }

    var onEventListView by remember {
        mutableStateOf<((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, R>) -> Unit)?>(
            null
        )
    }

    reloadKey?.let { key ->
        CollectSideEffect(
            sideEffect = viewModel.subscribeReloadData(key)
        ) {
            if (viewModel.checkReloadData(it)) {
                onEventListView?.invoke(ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.RefreshData)
            }
        }
    }

    BaseMVIScreen(viewModel = viewModel,
        handleSideEffect = {
        },
        renderContent = { uiState, onEvent ->
            var keywordSearch by remember { mutableStateOf("") }

            val colorScheme = LocalColorScheme.current
            val typography = LocalTypography.current

            LaunchedEffect(Unit) {
                if (uiState.ruleFilters.isNull()) {
                    onEvent(ListTransactionBaseViewEvent.UpdateRuleFilter(ruleFilterDefault))
                }
            }

            Column(
                Modifier
                    .background(Color.Transparent)
                    .fillMaxSize()
            ) {
                if (shouldShowSearchArea) {
                    SearchBox(onSearchValueChange = { keySearch ->
                        keywordSearch = keySearch
                        onEventListView?.invoke(
                            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
                                uiState.ruleFilters?.copySearch(keySearch)
                            )
                        )
                        onEvent(ListTransactionBaseViewEvent.UpdateRuleFilter(uiState.ruleFilters?.copySearch(keySearch) ?: ruleFilterDefault))
                    }, advSearchOnclick = {
                        showAdvSearch = true
                    },
                        hintText = hintTextSearch,
                        rightContent  = rightContent,
                        ruleFilter = uiState.ruleFilters ?: ruleFilterDefault
                    )
                }
                if (uiState.listDataState.isNotEmpty()) {
                    Box(
                        Modifier.padding(IBSpacing.spacingM),
                    ) {
                        Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        horizontal = IBSpacing.spacingXs
                                    )
                            ) {
                                if (isHaveCheckBoxSelectAll) {
                                    IBankCheckBox(
                                        indeterminate = getItemCheckedCount(uiState.listDataState) != uiState.totalListData,
                                        checked = getItemCheckedCount(uiState.listDataState) > 0,
                                        onCheckedChange = { data ->
                                            onEventListView?.invoke(
                                                ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.SelectAll(
                                                    data.checked,
                                                ),
                                            )
                                        })
                                Text(
                                    if (getItemCheckedCount(uiState.listDataState) > 0) stringResource(
                                        vn.com.bidv.localization.R.string.bo_chon
                                    ) else stringResource(vn.com.bidv.localization.R.string.chon_tat_ca),
                                    style = typography.titleTitle_s,
                                    color = colorScheme.contentMainSecondary,
                                    modifier = Modifier.padding(start = IBSpacing.spacingXs)
                                )
                                }
                                Box(Modifier.fillMaxWidth()) {
                                    Text(
                                        text = if (getItemCheckedCount(uiState.listDataState) > 0) {
                                            stringResource(
                                                vn.com.bidv.localization.R.string.da_chon_d,
                                                getItemCheckedCount(uiState.listDataState)
                                            ) + "/" + uiState.totalListData
                                        } else if ((uiState.totalListData ?: 0) > 0) {
                                            stringResource(
                                                resIdTextTransactionType,
                                                uiState.totalListData ?: 0
                                            )
                                        } else "",
                                        style = typography.bodyBody_m,
                                        color = colorScheme.contentMainPrimary,
                                        modifier = Modifier.align(Alignment.BottomEnd)
                                    )
                                }
                            }
                        }
                }

                if (uiState.isInitializedRuleFilter) {
                    Box(Modifier.weight(1f), contentAlignment = Alignment.BottomEnd) {
                        ListAutoLoadMore(
                            listState = lazyListState,
                            viewModel = loadListViewModel,
                            registerOnEvent = {
                                onEventListView = it
                            },
                            defaultRuleFilter = ruleFilterDefault,
                            onStateChange = { currentState ->
                                if (currentState.total.isNotNull()) {
                                    onEvent(
                                        ListTransactionBaseViewEvent.LoadDataSuccessList(
                                            listState = currentState.listItems,
                                            totalListData = currentState.total
                                        )
                                    )
                                }
                            },
                            emptyView = {
                                if (uiState.ruleFilters != ruleFilterDefault) {
                                    IBankEmptyState(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .padding(top = IBSpacing.spacing3xl),
                                        emptyStateType = EmptyStateType.SearchNoResult,
                                        backgroundColor = colorScheme.bgMainSecondary,
                                        verticalArrangement = Arrangement.Top,
                                        supportingText = textEmptySearch,
                                    )
                                } else NoDataLayout()
                            },
                        ) { item ->
                            itemContent(item, onEventListView)
                            Spacer(Modifier.height(IBSpacing.spacingM))
                        }

                        val itemCreateTransaction =
                            listAction.find { it.type == TypeBottomAction.CREATE_TRANSACTION }

                        if (getItemCheckedCount(uiState.listDataState) == 0 && itemCreateTransaction.isNotNull() && shouldShowCreateButton) {                            CreatingTransactionButton(
                                lazyListState,
                                navController,
                                {
                                    handleAction.invoke(
                                        itemCreateTransaction ?: ActionType.Create_Transaction,
                                        uiState.listDataState.filter { it.isChecked }
                                    )
                                }
                            )
                        }
                    }
                }

                if (getItemCheckedCount(uiState.listDataState) > 0) {
                    Box(Modifier.wrapContentHeight()) {
                        Box(
                            modifier = Modifier.align(Alignment.BottomCenter)
                        ) {
                            val buttonNegativeInfo = listAction
                                .find { it.type == TypeBottomAction.BOTTOM_NEGATIVE }
                                ?.let { item ->
                                    DialogButtonInfo(stringResource(item.titleRes)) {
                                        handleAction.invoke(
                                            item,
                                            uiState.listDataState.filter { it.isChecked })
                                    }
                                }

                            val typeNegativeButton = listAction
                                .find { it.type == TypeBottomAction.BOTTOM_NEGATIVE }
                                ?.let { item ->
                                    if (item == ActionType.Refuse || item == ActionType.Delete) {
                                       NormalButtonType.DESSECONDARYCOLOR(LocalColorScheme.current)
                                   } else {
                                       null
                                   }
                                }

                            val buttonPositiveInfo = listAction
                                .find { it.type == TypeBottomAction.BOTTOM_POSITIVE }
                                ?.let { item ->
                                    DialogButtonInfo(stringResource(item.titleRes)) {
                                        handleAction.invoke(
                                            item,
                                            uiState.listDataState.filter { it.isChecked })
                                    }
                                }

                            IBankActionBar(
                                isVertical = false,
                                buttonNegative = buttonNegativeInfo,
                                buttonPositive = buttonPositiveInfo,
                                typeNegativeButton = typeNegativeButton,
                            )
                        }
                    }
                }
            }
            if (showAdvSearch) {
                AdvSearchBottomSheet(
                    onDismiss = {
                        showAdvSearch = false
                    },
                    onApply = { rule ->
                        showAdvSearch = false
                        onEvent(ListTransactionBaseViewEvent.UpdateRuleFilter(rule))
                        onEventListView?.invoke(
                            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
                                rule
                            )
                        )
                    },
                    advSearchContent = { rule ->
                        advSearchContent(rule)
                    },
                    advSearchClearTitle = avdSearchClearTitle,
                    ruleFilter = uiState.ruleFilters ?: ruleFilterDefault,
                    ruleFilterDefault = ruleFilterDefault
                )
            }

        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <R : ListTransactionRuleFilter> AdvSearchBottomSheet(
    onDismiss: () -> Unit = {},
    onApply: (R) -> Unit = {},
    advSearchContent: @Composable (R) -> Unit = {},
    ruleFilter: R,
    ruleFilterDefault: R,
    advSearchClearTitle: String
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    var currentRuleFilter by remember { mutableStateOf(ruleFilter.copyGson<R>()) }

    IBankBottomSheet(
        title = stringResource(vn.com.bidv.localization.R.string.tim_kiem_nang_cao),
        onDismiss = {
            onDismiss.invoke()
        },
        bottomSheetContent = {
            Column(
                horizontalAlignment = Alignment.End, modifier = Modifier.padding(
                    top = IBSpacing.spacingXs,
                    end = IBSpacing.spacingM,
                    start = IBSpacing.spacingM
                )
            ) {

                advSearchContent(currentRuleFilter)

                Spacer(Modifier.height(IBSpacing.spacingM))

                Row(
                    horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorScheme.bgMainTertiary)
                ) {
                    IBankNormalButton(
                        modifier = Modifier.weight(1f),
                        size = NormalButtonSize.L(typography),
                        type = NormalButtonType.SECONDARYCOLOR(colorScheme),
                        text = advSearchClearTitle
                    ) {
                        currentRuleFilter = ruleFilterDefault.copyGson()
                    }

                    IBankNormalButton(
                        modifier = Modifier.weight(1f),
                        size = NormalButtonSize.L(typography),
                        type = NormalButtonType.PRIMARY(colorScheme),
                        text = stringResource(vn.com.bidv.localization.R.string.ap_dung)
                    ) {
                        onApply.invoke(currentRuleFilter)
                    }
                }

            }
        }

    )
}

@Composable
fun <R : ListTransactionRuleFilter> SearchBox(
    onSearchValueChange: (String) -> Unit = {},
    rightContent: @Composable ((R) -> Unit)? = null,
    advSearchOnclick: () -> Unit = {},
    hintText: String = stringResource(vn.com.bidv.localization.R.string.tim_kiem),
    ruleFilter: R
) {

    Row(
        modifier = Modifier.padding(
            horizontal = IBSpacing.spacingM,
            vertical = IBSpacing.spacingXs
        ),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {

        IBankInputSearchBase(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            placeHolderText = hintText,
            textValue = ruleFilter.search.orEmpty(),
            onTextChange = {

            },
            maxLength = MAX_LENGTH_LIST_TRANSACTION,
            onRequestChange = {
                onSearchValueChange.invoke(it)
            }
        )

        Spacer(Modifier.width(IBSpacing.spacingXs))

        if (ruleFilter.getBadgeNumber() > 0) {
            IBankBadgeButton(
                modifier = Modifier,
                buttonType = NormalButtonType.SECONDARYCOLOR(LocalColorScheme.current),
                icon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.nav_settings_outline),
                badgeNumber = ruleFilter.getBadgeNumber(),
                onClick = advSearchOnclick,
                badgeColor = LabelColor.BRAND
            )
        } else {
            IBankNormalButton(
                modifier = Modifier,
                size = NormalButtonSize.M(LocalTypography.current),
                type = NormalButtonType.SECONDARYGRAY(LocalColorScheme.current),
                leadingIcon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.nav_settings_outline),
                onClick = advSearchOnclick,
            )
        }
        if (rightContent != null) {
            Spacer(Modifier.width(IBSpacing.spacingXs))
            rightContent.invoke(ruleFilter)
        }
    }
}

@Composable
fun CreatingTransactionButton(
    listState: LazyListState,
    navController: NavHostController,
    eventCreateTransaction: () -> Unit = {}
) {
    val lineHeightPx = with(LocalDensity.current) { IBSpacing.spacingXs.toPx() }
    var hasScrolled by remember { mutableStateOf(false) }
    var textCreateButton by remember { mutableStateOf("") }

    LaunchedEffect(listState) {
        snapshotFlow { listState.firstVisibleItemIndex > 0 || listState.firstVisibleItemScrollOffset > lineHeightPx }
            .distinctUntilChanged()
            .collect { value ->
                hasScrolled = value
            }
    }
    textCreateButton = if (!hasScrolled) {
        //TODO Thêm Animation khi ẩn hiển text
        stringResource(R.string.init_payment)
    } else {
        ""
    }

    Box(
        Modifier
            .wrapContentHeight()
            .pinSnackBarPosition(
                snackBarPosition = SnackBarPosition.TopOut,
                margin = IBSpacing.spacingXs
            )
    ) {
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(end = IBSpacing.spacingM, bottom = IBSpacing.spacingM)
        ) {
            IBankNormalButton(
                modifier = Modifier,
                size = NormalButtonSize.L(LocalTypography.current),
                type = NormalButtonType.FLOATING(LocalColorScheme.current),
                text = textCreateButton.ifEmpty { null },
                leadingIcon = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.plus_outline)
            ) {
                eventCreateTransaction.invoke()
            }
        }
    }
}

@Composable
fun NoDataLayout() {
    val colorScheme = LocalColorScheme.current

    Box(Modifier.fillMaxSize()) {
        IBankEmptyState(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = IBSpacing.spacing3xl),
            verticalArrangement = Arrangement.Top,
            emptyStateType = EmptyStateType.EmptyBox,
            backgroundColor = colorScheme.bgMainSecondary,
            supportingText = stringResource(R.string.khong_co_du_lieu),
        )
    }
}

fun <T> getItemCheckedCount(list: List<ModelCheckAble<T>>): Int {
    return list.count { it.isChecked }
}

@Composable
fun <T, R : RuleFilters> ItemCardCommon(
    transactionsDMO: ModelCheckAble<T>,
    uiState: ViewState?,
    onEvent: ((ListTransactionBaseViewEvent<T, R>) -> Unit)?,
    onEventListView: ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, R>) -> Unit)?,
    onClickItem: () -> Unit = {},
    navController: NavHostController,
    listAction: List<ActionType> = emptyList(),
    shouldShowItemCheckBox: Boolean = true,
    headerActionIconId: Int = vn.com.bidv.designsystem.R.drawable.more_vertical_filled,
    handleAction: (ActionType) -> Unit,
    contentHeader: @Composable (ModelCheckAble<T>) -> Unit = {},
    contentBody: @Composable (ModelCheckAble<T>) -> Unit = {},
) {
    val colorScheme = LocalColorScheme.current
    Box(
        Modifier
            .padding(horizontal = IBSpacing.spacingM)
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .background(color = colorScheme.bgMainTertiary)
    ) {
        Column(
            Modifier
                .clickable {
                    onClickItem()
                }) {
            ItemCardHeaderCommon(
                transactionsDMO,
                uiState,
                onEventListView,
                shouldShowItemCheckBox = shouldShowItemCheckBox,
                headerActionIconId = headerActionIconId,
                contentHeader = contentHeader,
                listAction = listAction,
                handleAction = handleAction
            )
            HorizontalDivider(
                color = colorScheme.borderMainSecondary, thickness = IBBorderDivider.borderDividerS
            )
            Spacer(Modifier.padding(top = IBSpacing.spacingXs))
            contentBody(transactionsDMO)
        }
    }
}

@Composable
fun <T, R : RuleFilters> ItemCardHeaderCommon(
    transactionsDMO: ModelCheckAble<T>,
    uiState: ViewState?,
    onEventListView: ((ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, R>) -> Unit)?,
    contentHeader: @Composable (ModelCheckAble<T>) -> Unit = {},
    listAction: List<ActionType> = emptyList(),
    shouldShowItemCheckBox: Boolean = true,
    headerActionIconId: Int = vn.com.bidv.designsystem.R.drawable.more_vertical_filled,
    handleAction: (ActionType) -> Unit
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    var isShowDropDown by remember { mutableStateOf(false) }


    Column(
        modifier = Modifier.padding(
            start = IBSpacing.spacingM,
            end = IBSpacing.spacingXs
        )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.padding(
                top = IBSpacing.spacingS
            )
        ) {
            if (shouldShowItemCheckBox) {
                IBankCheckBox(
                indeterminate = false,
                checked = transactionsDMO.isChecked,
                onCheckedChange = {
                    onEventListView?.invoke(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.SelectItem(
                            transactionsDMO
                        )
                    )
                },
                )
            }

            Box(modifier = Modifier.weight(1f)) {
                contentHeader(transactionsDMO)
            }

            if (listAction.isNotEmpty()) {
                Box(
                    Modifier
                        .width(20.dp)
                        .clickable {
                            isShowDropDown = true
                        }) {
                    Image(
                        painterResource(id = headerActionIconId),
                        contentDescription = "",
                        Modifier
                            .size(20.dp)
                            .align(Alignment.BottomEnd)
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
        Box(Modifier.align(Alignment.End)) {
            DropdownMenu(
                modifier = Modifier.background(colorScheme.bgMainTertiary),
                expanded = isShowDropDown,
                onDismissRequest = {
                    isShowDropDown = false
                }) {
                listAction.forEachIndexed { index, item ->
                    if (item == ActionType.Create_Transaction) {
                        return@forEachIndexed
                    }

                    DropdownMenuItem(text = {
                        IBankContextMenu(
                            title = stringResource(item.titleRes),
                            leadingIconResId = item.resID,
                        )
                    },
                        onClick = {
                            handleAction.invoke(item)
                            isShowDropDown = false
                        })
                }
            }
        }
    }

}





