package vn.com.bidv.feature.common.ui.screen.commontransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.MenuDMO
import javax.inject.Inject

@HiltViewModel
class TransactionReportViewModel @Inject constructor(
    private val getMenuUseCase: UserInfoUseCase,
    itemBuilders: Set<@JvmSuppressWildcards TransactionBaseBuilder>,
    reducer: TransactionBaseReducer
) : TransactionBaseViewModel(
    reducer = reducer,
    itemBuilders = itemBuilders,
) {
    override fun fetchData(
        onLoadSuccess: (data: List<MenuDMO>?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return callDomain(
            showLoadingIndicator = false, // Loading offline
            onSuccess = { onLoadSuccess(it.data) }
        ) {
            getMenuUseCase.getReportMenus()
        }
    }
}
