package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.data.ManageApprovalRequestsRepository
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.Action
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ManageApprovalRequestsVerifyUseCase @Inject constructor(
    private val manageApprovalRequestsRepository: ManageApprovalRequestsRepository
) : VerifyByTypeTransactionUseCase {
    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        val result =
            manageApprovalRequestsRepository.performGetApprovalRequestsData(input.txnIds)
        val domain = result.convert(TransAuthDMO::class.java)
        return if (domain is DomainResult.Success) {
            DomainResult.Success(InitVerifyTransactionResponse(transAuth = domain.data))
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        val result =
            manageApprovalRequestsRepository.performVerifyApprovalRequest(initResponse.transKey ?: initResponse.transAuth?.transKey ?: "", reqValue ?: "")
        val domain = result.convert(AdminTransVerifyDMO::class.java)
        return if (domain is DomainResult.Success) {
            val data = domain.getSafeData()?.copy(actionType = Action.APPROVE.name)
            DomainResult.Success(Gson().toJson(data))
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }
}