package vn.com.bidv.feature.login.ui.userinfo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import com.google.gson.JsonObject
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.datadisplay.row.IBankRowList
import vn.com.bidv.designsystem.component.datadisplay.row.IBankRowListLeadingElement
import vn.com.bidv.designsystem.component.datadisplay.row.IBankRowListTrailingElement
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.navigation.NavigationHelper.navigateToVerifyByTypeCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.userinfo.UserInfoReducer.UserInfoViewEvent
import vn.com.bidv.feature.login.ui.userinfo.UserInfoReducer.UserInfoViewState
import vn.com.bidv.feature.login.ui.userinfo.modelui.iconMap
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.formatMoney
import vn.com.bidv.feature.login.constants.Constants as ConstantsLogin
import vn.com.bidv.localization.R as RLocalization

@Composable
fun UserInfoScreen(navController: NavHostController) {
    val vm: UserInfoViewModel = hiltViewModel()
    val context = LocalContext.current
    var errorMessage by remember { mutableStateOf("") }

    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            // Handle side effect
            VerifyOtpHandler(
                vm = vm,
                onEvent = onEvent,
            )
            // Render content
            UserInfoMainContent(uiState, onEvent, errorMessage)
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is UserInfoReducer.UserInfoViewEffect.PositivePerformOtpAuth -> {
                    NavigationHelper.navigateToPositiveUserInfoSmsOTP(
                        navController = navController,
                        sideEffect.modelLoginDMO,
                        sideEffect.modelCreateOtpResDMO
                    )
                }

                is UserInfoReducer.UserInfoViewEffect.PositivePerformStrongOTPAuth -> {
                    navigateToVerifyByTypeCreateTransaction(
                        navController,
                        dataString = JsonObject().toString(),
                        type = VerifyTransactionTypeConstant.UserInfo,
                    )
                }

                is UserInfoReducer.UserInfoViewEffect.GetUserInfoSettingFail -> {
                    errorMessage = sideEffect.error
                        ?: context.getString(RLocalization.string.co_loi_xay_ra_vui_long_thu_lai)
                }

                else -> {
                    // DO Nothing
                }
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.thong_tin_nguoi_dung),
        ),
    )
}

@Composable
private fun VerifyOtpHandler(
    vm: UserInfoViewModel,
    onEvent: (UserInfoViewEvent) -> Unit,
) {
    var verifyOtpMessageError by remember { mutableStateOf("") }

    CollectSideEffect(vm.subscribeShareData(ConstantsLogin.USER_INFO_SUCCESS)) {
        if (it.key == ConstantsLogin.USER_INFO_SUCCESS) {
            onEvent(
                UserInfoViewEvent.OnApprovalTransactionSuccess(
                    userPersonalInfoDMO = it.data.let { data ->
                        Gson().fromJson(
                            data,
                            UserPersonalInfoDMO::class.java
                        )
                    },
                )
            )
        }
    }

    if (verifyOtpMessageError.isNotEmpty()) {
        IBankModalConfirm(
            title = stringResource(RLocalization.string.loi),
            modalConfirmType = ModalConfirmType.Error,
            supportingText = verifyOtpMessageError,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.thu_lai),
                    onClick = {
                        onEvent(UserInfoViewEvent.OnGetUserInfoSecure)
                    }
                )
            ),
            onDismissRequest = {
                verifyOtpMessageError = ""
            }
        )
    }
}

@Composable
private fun UserInfoMainContent(
    uiState: UserInfoViewState,
    onEvent: (UserInfoViewEvent) -> Unit,
    errorMessage: String
) {
    if (!uiState.isInitSuccess) {
        LaunchedEffect(Unit) {
            onEvent(UserInfoViewEvent.OnInitData)
        }
    }
    if (uiState.userInfoModelUI.userInfoSettingsDMO != null) {
        UserInfoContent(uiState, onEvent)
    } else if (uiState.isInitSuccess) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            IBankEmptyState(
                modifier = Modifier
                    .fillMaxSize(),
                emptyStateType = EmptyStateType.SearchNoResult,
                backgroundColor = LocalColorScheme.current.bgMainSecondary,
                verticalArrangement = Arrangement.Center,
                supportingText = errorMessage,
                textButton = stringResource(RLocalization.string.thu_lai),
                onClickButton = {
                    onEvent(UserInfoViewEvent.OnInitData)
                }
            )
        }
    }
}

@Composable
private fun UserInfoContent(
    uiState: UserInfoViewState,
    onEvent: (UserInfoViewEvent) -> Unit,
) {
    val userInfo = uiState.userInfoModelUI.userInfoSettingsDMO?.userInfo
    val cusInfo = uiState.userInfoModelUI.userInfoSettingsDMO?.cusInfo

    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(IBSpacing.spacingM)
            .fillMaxSize()
    ) {
        InfoCard(
            title = stringResource(RLocalization.string.thong_tin_ca_nhan) + ": " + userInfo?.name,
            infoSecureContent = {
                if (uiState.userInfoModelUI.userInfoSecureDMO != null) {
                    UserInfoSecure(uiState)
                } else {
                    IBankEmptyState(
                        emptyStateType = EmptyStateType.Lock,
                        supportingText = stringResource(RLocalization.string.thong_tin_bi_an_vi_ly_do_bao_mat_vui_long_xac_thuc_de_xem_thong_tin),
                        textButton = stringResource(RLocalization.string.xac_thuc),
                        onClickButton = {
                            onEvent(UserInfoViewEvent.OnGetUserInfoSecure)
                        }
                    )
                }
            },
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingM))

        InfoCard(
            title = stringResource(RLocalization.string.thong_tin_phan_quyen_su_dung_dich_vu),
            items = listOf(
                RLocalization.string.ten_dang_nhap to userInfo?.username,
                RLocalization.string.phuong_thuc_nhan_otp to userInfo?.otpType,
                RLocalization.string.phuong_thuc_xac_thuc to userInfo?.authType,
                RLocalization.string.nhom_quyen to userInfo?.role,
                RLocalization.string.cap_phe_duyet to userInfo?.wfRole,
                RLocalization.string.han_muc_nguoi_dung to userInfo?.limit.toString().formatMoney(
                    SdkBaseConstants.MoneyCurrencyConstants.VND
                ),
            )
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingM))

        InfoCard(
            title = stringResource(RLocalization.string.thong_tin_to_chuc),
            items = listOf(
                RLocalization.string.so_cif to cusInfo?.cifNo,
                RLocalization.string.ten_to_chuc to cusInfo?.name,
                RLocalization.string.so_dang_ky_doanh_nghiepquyet_dinh_thanh_lap to cusInfo?.idReg,
            )
        )
    }
}

@Composable
private fun UserInfoSecure(uiState: UserInfoViewState) {
    val userInfoSecure = uiState.userInfoModelUI.userInfoSecureDMO
    val (titleReceiverOtp, descReceiverOtp) = if (userInfoSecure?.emailOtpReg == true) {
        Pair(
            RLocalization.string.email_nhan_otp,
            userInfoSecure.otpEmail,
        )
    } else {
        Pair(
            RLocalization.string.so_dien_thoai,
            userInfoSecure?.mobile,
        )
    }

    val titleIdType = if (userInfoSecure?.idType == "IDENTITY_CARD") {
        RLocalization.string.so_can_cuoc_cong_dan
    } else {
        RLocalization.string.so_ho_chieu
    }

    val descIdExpDate = if (userInfoSecure?.idExpType == true && userInfoSecure.idExpDate != null) {
        userInfoSecure.idExpDate
    } else stringResource(
        RLocalization.string.khong_xac_dinh_thoi_han
    )

    Column(
        modifier = Modifier
            .padding(IBSpacing.spacingM)
    ) {
        InfoSecureCardBorder(
            items = listOf(
                titleReceiverOtp to descReceiverOtp,
                RLocalization.string.email to userInfoSecure?.email,
            )
        )

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))

        InfoSecureCardBorder(
            items = listOf(
                titleIdType to userInfoSecure?.idNum,
                RLocalization.string.ngay_cap to userInfoSecure?.idIssueDate,
                RLocalization.string.ngay_het_han to descIdExpDate,
                RLocalization.string.quoc_tich to userInfoSecure?.nat,
            )
        )
    }
}

@Composable
private fun InfoCard(
    title: String,
    items: List<Pair<Int, String?>>? = null,
    infoSecureContent: @Composable (() -> Unit?)? = null,
) {
    Column(
        modifier = Modifier
            .background(
                LocalColorScheme.current.bgMainTertiary,
                shape = RoundedCornerShape(size = 12.dp)
            )
            .fillMaxWidth()
    ) {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = title,
        )

        HorizontalDivider(
            color = LocalColorScheme.current.borderMainPrimary,
            thickness = IBBorderDivider.borderDividerXs
        )

        if (infoSecureContent.isNotNull()) {
            infoSecureContent?.invoke()
        } else {
            Column(
                modifier = Modifier
                    .padding(vertical = IBSpacing.spacingXs)
            ) {
                items?.forEach { (key, value) ->
                    if (!value.isNullOrBlank()) {
                        UserInfoRowList(
                            title = stringResource(key),
                            description = value,
                            icon = iconMap[key],
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun InfoSecureCardBorder(items: List<Pair<Int, String?>>?) {
    Column(
        modifier = Modifier
            .border(
                width = IBBorderDivider.borderDividerS,
                color = LocalColorScheme.current.borderMainPrimary,
                shape = RoundedCornerShape(size = IBCornerRadius.cornerRadiusL)
            )
            .padding(vertical = IBSpacing.spacingXs)
    ) {
        items?.forEach { (key, value) ->
            if (!value.isNullOrBlank()) {
                UserInfoRowList(
                    title = stringResource(key),
                    description = value,
                    icon = iconMap[key],
                )
            }
        }
    }
}

@Composable
private fun UserInfoRowList(title: String, description: String, icon: Int?) {
    val leadingElement = if (icon != null) {
        IBankRowListLeadingElement.Icon(
            ImageVector.vectorResource(
                id = icon
            )
        )
    } else {
        IBankRowListLeadingElement.None
    }
    IBankRowList(
        labelText = title,
        descriptionText = description,
        leadingElement = leadingElement,
        labelTextStyle = LocalTypography.current.bodyBody_m.copy(
            color = LocalColorScheme.current.contentMainTertiary
        ),
        descriptionTextStyle = LocalTypography.current.labelLabel_xl.copy(
            color = LocalColorScheme.current.contentMainPrimary
        ),
        trailingElement = IBankRowListTrailingElement.None
    )
}



