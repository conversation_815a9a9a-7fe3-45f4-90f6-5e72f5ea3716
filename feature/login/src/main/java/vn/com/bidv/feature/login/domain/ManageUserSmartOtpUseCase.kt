package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.login.data.SmartOTPRepository
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ManageUserSmartOtpUseCase @Inject constructor(private val smartOTPRepository: SmartOTPRepository) {

    suspend fun deleteUserSmartOtp(userId: String, smToken: String): DomainResult<Boolean> {
        val result = smartOTPRepository.deleteUserSmartOtp(userId, smToken)
        val domain = result.convert {
            result is NetworkResult.Success
        }
        return domain
    }
}