package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.common.data.utilities.model.SmartOtpActiveRequest
import vn.com.bidv.feature.common.data.utilities.model.SmartOtpRqActiveRequest
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.SmartOTPErrorCode
import vn.com.bidv.feature.login.data.SmartOTPRepository
import vn.com.bidv.feature.login.domain.model.SmartOtpActiveResDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpApprovePendingDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpInfoDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpReqActiveResDMO
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

const val OS = "ANDROID"

class ActiveSmartOTPUseCase @Inject constructor(
    private val smartOTPRepository: SmartOTPRepository,
    private val networkConfig: NetworkConfig,
    private val localRepository: LocalRepository,
	private val userInfoUseCase: UserInfoUseCase
) {
    suspend operator fun invoke(): DomainResult<SmartOtpReqActiveResDMO> {
        val smartOtpRqActiveRequest = SmartOtpRqActiveRequest(
            deviceId = networkConfig.deviceId,
            os = OS,
            osVersion = networkConfig.osVersion,
            deviceModel = networkConfig.deviceModel,
        )
        val networkResult =
            smartOTPRepository.requestActiveSmartOtp(
                smartOtpRqActiveRequest = smartOtpRqActiveRequest
            )

        if (networkResult is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(networkResult.errorCode),
                errorMessage = networkResult.errorMessage
            )
        }
        return networkResult.convert(SmartOtpReqActiveResDMO::class.java)

    }

    suspend fun activeSmartOtp(otpNum: String, fcmId: String): DomainResult<SmartOtpActiveResDMO> {
        val smartOtpActiveRequest = SmartOtpActiveRequest(
            deviceId = networkConfig.deviceId,
            os = OS,
            osVersion = networkConfig.osVersion,
            deviceModel = networkConfig.deviceModel,
            otp = otpNum,
            fcmId = fcmId
        )
        val result =
            smartOTPRepository.activeSmartOtp(
                smartOtpActiveRequest = smartOtpActiveRequest
            )

        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        }
        return result.convert(SmartOtpActiveResDMO::class.java)
    }

    suspend fun deleteAllSmartOtpInDevice(): DomainResult<Boolean> {
        val result =
            smartOTPRepository.deleteAllSmartOtpInDevice()
        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        }
        return DomainResult.Success(true)
    }

    suspend fun changePin(): DomainResult<TransUpdateBasicOtpDMO> {
        val result = smartOTPRepository.changePin(
            deviceId = networkConfig.deviceId,
        )
        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        }

        return result.convert(TransUpdateBasicOtpDMO::class.java)
    }

    suspend fun verifyBasicOtp(
        transKey: String,
        authValue: String
    ): DomainResult<Boolean> {
        val result =
            smartOTPRepository.verifyBasicOtp(
                transKey = transKey,
                authValue = authValue,
            )

        return result.convert { result is NetworkResult.Success }
    }

    suspend fun shareChangePinSuccessAction(action: String) {
        localRepository.shareDataTo(
            key = Constants.CHANGE_PIN_SUCCESS,
            data = ShareDataDTO(
                key = action,
                data = action
            )
        )
    }

    private fun handleError(errorCode: String): String {
        return when (errorCode) {
            SmartOTPErrorCode.ERROR_DURING_PROCESSING,
            SmartOTPErrorCode.USER_NOT_REGISTER_YET,
            SmartOTPErrorCode.SMART_OTP_STATUS_NOT_VALID,
            SmartOTPErrorCode.INPUT_INVALID -> SmartOtpErrorType.COMMON_ERROR

            SmartOTPErrorCode.DEVICE_THAT_IS_ACTIVATING_THE_USER_SMART_OTP_HAS_A_DIFFERENT_ID ->
                SmartOtpErrorType.ACTIVATION_DIFFERENT_USER_SMART_OTP

            SmartOTPErrorCode.SMART_OTP_HAS_BEEN_LOCKED -> SmartOtpErrorType.SMART_OTP_HAS_BEEN_LOCKED

            else -> errorCode
        }
    }

    private fun handleErrorConvertActive(errorCode: String): String {
        return when (errorCode) {
            SmartOTPErrorCode.ERROR_DURING_PROCESSING,
            SmartOTPErrorCode.INPUT_INVALID,
            SmartOTPErrorCode.USER_NOT_REGISTER_YET,
            SmartOTPErrorCode.SMART_OTP_STATUS_NOT_VALID,
            SmartOTPErrorCode.STATUS_INVALID_FOR_VERIFICATION,
            SmartOTPErrorCode.VERIFICATION_FAILED -> SmartOtpErrorType.COMMON_ERROR

            SmartOTPErrorCode.SMART_OTP_LOCKED_TOO_MANY_ATTEMPTS,
            SmartOTPErrorCode.SMART_OTP_REACTIVATE_OR_VISIT_BRANCH
                -> SmartOtpErrorType.SMART_OTP_HAS_BEEN_LOCKED

            else -> errorCode
        }
    }

    suspend fun requestActiveRetry(): DomainResult<TransUpdateBasicOtpDMO> {
        val result = smartOTPRepository.requestActiveRetry()
        val domain = result.convert(TransUpdateBasicOtpDMO::class.java)
        return domain
    }

    suspend fun approvePending(
        transKey: String,
        authValue: String
    ): DomainResult<SmartOtpApprovePendingDMO> {
        val result = smartOTPRepository.approvePending(transKey, authValue)
        val domain = result.convert(SmartOtpApprovePendingDMO::class.java)
        return domain
    }

    suspend fun clearDataInputPin() {
        localRepository.shareDataTo(
            Constants.CLEAR_INPUT_PIN, ShareDataDTO(
                Constants.CLEAR_INPUT_PIN,
                ""
            )
        )
    }

    suspend fun needAllSmartOtpSuccess() {
        localRepository.shareDataTo(
            Constants.CLEAR_INPUT_PIN, ShareDataDTO(
                Constants.NEED_DELETE_ALL_SMART_OTP_SUCCESS,
                Constants.NEED_DELETE_ALL_SMART_OTP_SUCCESS
            )
        )
    }

    suspend fun getSmartOtpInfo(isGetSmartOtpInfo: Boolean): DomainResult<SmartOtpInfoDMO?> {
        val userInfo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user
        return if (isGetSmartOtpInfo) {
            val result = smartOTPRepository.getSmartOtpInfo()
            val domain = result.convert(SmartOtpInfoDMO::class.java)
            if (domain is DomainResult.Error) {
                domain
            } else {
                DomainResult.Success(
                    SmartOtpInfoDMO(
                        user = userInfo,
                        smartOtp = domain.getSafeData()?.smartOtp,
                        userRole = domain.getSafeData()?.userRole,
                    )
                )
            }
        } else {
            DomainResult.Success(SmartOtpInfoDMO(user = userInfo))
        }
    }

    suspend fun convertActive(otp: String): DomainResult<Boolean> {
        val result = smartOTPRepository.convertActive(otp = otp)
        if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleErrorConvertActive(result.errorCode),
                errorMessage = result.errorMessage
            )
        }
        return DomainResult.Success(true)
    }

}

object SmartOtpErrorType {
    const val SMART_OTP_HAS_BEEN_LOCKED = "SMART_OTP_HAS_BEEN_LOCKED"
    const val COMMON_ERROR = "COMMON_ERROR"
    const val ACTIVATION_DIFFERENT_USER_SMART_OTP = "ACTIVATION_DIFFERENT_USER_SMART_OTP"
    const val DEFAULT_ERROR = "DEFAULT_ERROR"
}
