package vn.com.bidv.feature.login.ui.smsOTP.smsotpbase

import vn.com.bidv.feature.login.domain.model.IIBankModelOTPDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer.BaseIBankModalOTPViewState
import vn.com.bidv.feature.login.ui.smsOTP.smsreceiver.SMSReceiver
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

/**
 *
 * @param I is input from previous screen
 * @param R is output to resend otp
 * @param T is output to verify otp
 *
 */

abstract class BaseIBankModalOTPViewModel<I : IIBankModelOTPDMO, R: IIBankModelOTPDMO, T>(
    initialState: BaseIBankModalOTPViewState<I, R, T>,
    reducer: BaseIBankModalOTPReducer<I, R, T>
) :
    ViewModelIBankBase<BaseIBankModalOTPViewState<I, R, T>, BaseIBankModalOTPViewEvent<I, R, T>, BaseIBankModalOTPViewEffect<I, R, T>>(
        initialState = initialState,
        reducer = reducer
    ) {

    var smsReceiver: SMSReceiver? = null

    override fun handleEffect(
        sideEffect: BaseIBankModalOTPViewEffect<I, R, T>,
        onResult: (BaseIBankModalOTPViewEvent<I, R, T>) -> Unit
    ) {
        // TODO: Controller logic, do not handle logic code here.
        /*
		 when (sideEffect) {
		    // Continue event
            is BaseIBankModalOTPViewEffect.FetchListData -> {
                // Controller logic via Use Case
                fetchData(sideEffect.searchText) { nextEvent ->
                    onResult(nextEvent)
                }
            }

            else -> {}
        }
		*/
    }
}
