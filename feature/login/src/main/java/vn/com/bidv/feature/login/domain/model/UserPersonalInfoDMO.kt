package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName

data class UserPersonalInfoDMO (
    @SerializedName("mobile")
    val mobile: kotlin.String? = null,

    @SerializedName("emailOtpReg")
    val emailOtpReg: kotlin.Boolean? = null,

    @SerializedName("otpEmail")
    val otpEmail: kotlin.String? = null,

    @SerializedName("email")
    val email: kotlin.String? = null,

    @SerializedName("idType")
    val idType: kotlin.String? = null,

    @SerializedName("idNum")
    val idNum: kotlin.String? = null,

    @SerializedName("nat")
    val nat: kotlin.String? = null,

    @SerializedName("idIssueDate")
    val idIssueDate: kotlin.String? = null,

    @SerializedName("idExpType")
    val idExpType: kotlin.Boolean? = null,

    @SerializedName("idExpDate")
    val idExpDate: kotlin.String? = null
)