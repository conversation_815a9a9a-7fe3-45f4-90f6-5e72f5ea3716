package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.data.UserInfoSettingRepository
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.domain.model.UserInfoSettingsDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class UserInfoSettingUseCase @Inject constructor(
    private val userInfoSettingRepository: UserInfoSettingRepository,
    private val localRepository: LocalRepository,
) {
    suspend fun getUserInfoSettings(): DomainResult<UserInfoSettingsDMO> {
        val result = userInfoSettingRepository.getUserInfoSettings()
        val userInfoSettingsDMO = result.convert(UserInfoSettingsDMO::class.java)
        return userInfoSettingsDMO
    }

    suspend fun getUserInfoSettingSecure(): DomainResult<CompositeOtpResDMO> {
        val result = userInfoSettingRepository.getUserInfoSettingSecure()
        val compositeOtpResDMO = result.convert(CompositeOtpResDMO::class.java)
        return compositeOtpResDMO
    }

    suspend fun verifySmartOtpUserInfo(
        transId: String,
        otp: String
    ): DomainResult<UserPersonalInfoDMO> {
        val result = userInfoSettingRepository.verifySmartOtpUserInfo(transId, otp)
        val userPersonalInfoDMO = result.convert(UserPersonalInfoDMO::class.java)
        return userPersonalInfoDMO
    }

    suspend fun shareUserInfoSuccessAction(action: String, data: UserPersonalInfoDMO?) {
        localRepository.shareDataTo(
            key = Constants.USER_INFO_SUCCESS,
            data = ShareDataDTO(
                key = action,
                data = Gson().toJson(data)
            )
        )
    }
}