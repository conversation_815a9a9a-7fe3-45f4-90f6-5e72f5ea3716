package vn.com.bidv.feature.login.ui.manageapprovalrequest

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.domain.model.TransRequestApprovalDMO
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.Action

class ManageApprovalRequestsReducer :
    Reducer<ManageApprovalRequestsReducer.ManageApprovalRequestsViewState, ManageApprovalRequestsReducer.ManageApprovalRequestsViewEvent, ManageApprovalRequestsReducer.ManageApprovalRequestsViewEffect> {

    @Immutable
    data class ManageApprovalRequestsViewState(
        val isInitSuccess: Boolean = false,
        val listTranRequestApproval: List<ModelCheckAble<TransRequestApprovalDMO>> = emptyList(),
        val transAuthDMO: TransAuthDMO = TransAuthDMO(),
        val listTranRequestApprovalProcessing: List<TransRequestApprovalDMO> = emptyList(),
    ) : ViewState

    @Immutable
    sealed class ManageApprovalRequestsViewEvent : ViewEvent {

        data class OnGetListTxnDataSuccess(val listTranRequestApproval: List<ModelCheckAble<TransRequestApprovalDMO>>) :
            ManageApprovalRequestsViewEvent()

        data class OnRejectTransaction(val ids: List<String>, val reason: String, val actionType: String) :
            ManageApprovalRequestsViewEvent()

        data class OnRejectTransactionSuccess(val data: AdminTransVerifyDMO) :
            ManageApprovalRequestsViewEvent()

        data class OnShowConfirmPopup(val action: Action, val selectedTransactions: List<TransRequestApprovalDMO>) :
            ManageApprovalRequestsViewEvent()

        data class OnShowBottomSheet(val transRequestApprovalDMO: TransRequestApprovalDMO) :
            ManageApprovalRequestsViewEvent()
    }

    @Immutable
    sealed class ManageApprovalRequestsViewEffect : SideEffect {

        data class RejectTransaction(val ids: List<String>, val actionType: String, val reason: String) :
            ManageApprovalRequestsViewEffect()

        data class RejectTransactionSuccess(val data: AdminTransVerifyDMO) :
            ManageApprovalRequestsViewEffect(), UIEffect

        data class ShowConfirmPopup(val action: Action, val selectedTransactions: List<TransRequestApprovalDMO>) :
            ManageApprovalRequestsViewEffect(), UIEffect

        data class ShowDetailBottomSheet(val transRequestApprovalDMO: TransRequestApprovalDMO) :
            ManageApprovalRequestsViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: ManageApprovalRequestsViewState,
        event: ManageApprovalRequestsViewEvent,
    ): Pair<ManageApprovalRequestsViewState, ManageApprovalRequestsViewEffect?> {
        return handleManageApprovalRequestsViewState(previousState, event)
    }

    private fun handleManageApprovalRequestsViewState(
        previousState: ManageApprovalRequestsViewState,
        event: ManageApprovalRequestsViewEvent
    ): Pair<ManageApprovalRequestsViewState, ManageApprovalRequestsViewEffect?> {
        return when (event) {
            is ManageApprovalRequestsViewEvent.OnGetListTxnDataSuccess -> {
                previousState.copy(listTranRequestApproval = event.listTranRequestApproval) to null
            }

            is ManageApprovalRequestsViewEvent.OnRejectTransaction -> {
                previousState.copy(
                ) to ManageApprovalRequestsViewEffect.RejectTransaction(event.ids, event.actionType, event.reason)
            }

            is ManageApprovalRequestsViewEvent.OnRejectTransactionSuccess -> {
                previousState.copy(
                ) to ManageApprovalRequestsViewEffect.RejectTransactionSuccess(event.data)
            }

            is ManageApprovalRequestsViewEvent.OnShowBottomSheet -> {
                previousState to ManageApprovalRequestsViewEffect.ShowDetailBottomSheet(event.transRequestApprovalDMO)
            }

            is ManageApprovalRequestsViewEvent.OnShowConfirmPopup -> {
                previousState to ManageApprovalRequestsViewEffect.ShowConfirmPopup(event.action, event.selectedTransactions)
            }
        }
    }
}
