package vn.com.bidv.feature.login.ui.forgotpassword

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.CodeValueDMO
import vn.com.bidv.feature.login.domain.model.CreateForgotPwResponseDMO

enum class MethodOtp(val value: String) {
    SMS("SMS"),
    EMAIL("EMAIL"),
}

class ForgotPasswordReducer :
    Reducer<ForgotPasswordReducer.ForgotPasswordViewState, ForgotPasswordReducer.ForgotPasswordViewEvent, ForgotPasswordReducer.ForgotPasswordViewEffect> {

    data class ForgotPasswordViewState(
        val username: String = "",
        val idNumber: String = "",
        val expirationDate: String = "",
        val methodOtp: MethodOtp = MethodOtp.SMS,
        val isIndefinite: Boolean = false,
        val contactInfo: String = "",
        val registrationNumber: String = "",
        var listOtherInputClientErrorCode: List<Pair<String, String>>? = null,
    ) : ViewState

    @Immutable
    sealed class ForgotPasswordViewEvent : ViewEvent {
        data class OnInputUsernameChangeEvent(val username: String) : ForgotPasswordViewEvent()
        data class OnInputIdNumberChangeEvent(val idNumber: String) : ForgotPasswordViewEvent()
        data class OnInputExpirationDateChangeEvent(val expirationDate: String) :
            ForgotPasswordViewEvent()

        data class OnInputMethodOtpChangeEvent(val methodOtp: MethodOtp) : ForgotPasswordViewEvent()

        data class OnInputIsIndefiniteChangeEvent(val isIndefinite: Boolean) : ForgotPasswordViewEvent()

        data class OnInputContactInfoChangeEvent(val contactInfo: String) :
            ForgotPasswordViewEvent()

        data class OnInputRegistrationNumberChangeEvent(val registrationNumber: String) :
            ForgotPasswordViewEvent()

        data object OnInputContactInfoValidEvent : ForgotPasswordViewEvent()

        data object ValidOtherInputDoneSuccess : ForgotPasswordViewEvent()
        data object ValidEmailOrPhoneNumberInputDoneSuccess : ForgotPasswordViewEvent()

        data class ValidOtherInputDoneFail(val listClientErrorCode: List<Pair<String, String>>) :
            ForgotPasswordViewEvent()

        data object ValidateDataEvent : ForgotPasswordViewEvent()

        data class SubmitDataSuccessEvent(val data: CreateForgotPwResponseDMO?) :
            ForgotPasswordViewEvent()

    }

    @Immutable
    sealed class ForgotPasswordViewEffect : SideEffect {
        data class ValidPhoneNumberEffect(val phoneNumber: String) : ForgotPasswordViewEffect()

        data class ValidEmailEffect(val email: String) : ForgotPasswordViewEffect()

        data class ValidateDataEffect(
            val username: String,
            val idNumber: String,
            val expirationDate: String,
            val methodOtp: MethodOtp,
            val contactInfo: String,
            val registrationNumber: String,
            val isIndefinite: Boolean
        ) : ForgotPasswordViewEffect()

        data class SubmitDataEffect(
            val username: String,
            val idNumber: String,
            val expirationDate: String,
            val methodOtp: MethodOtp,
            val contactInfo: String,
            val registrationNumber: String
        ) : ForgotPasswordViewEffect()

        data class GoToVerifyQuestionEffect(
            val username: String?,
            val txnId: String?,
            val listQuestions : List<CodeValueDMO>
        ) : ForgotPasswordViewEffect(), UIEffect

        data class GoToSmsOtpEffect(
            val data: CreateForgotPwResponseDMO?,
            val username: String
        ) : ForgotPasswordViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: ForgotPasswordViewState,
        event: ForgotPasswordViewEvent,
    ): Pair<ForgotPasswordViewState, ForgotPasswordViewEffect?> {
        return when (event) {
            is ForgotPasswordViewEvent.OnInputUsernameChangeEvent -> {
                previousState.copy(username = event.username) to null
            }

            is ForgotPasswordViewEvent.OnInputIdNumberChangeEvent -> {
                previousState.copy(idNumber = event.idNumber) to null
            }

            is ForgotPasswordViewEvent.OnInputExpirationDateChangeEvent -> {
                previousState.copy(expirationDate = event.expirationDate) to null
            }

            is ForgotPasswordViewEvent.OnInputMethodOtpChangeEvent -> {
                previousState.copy(
                    methodOtp = event.methodOtp,
                    contactInfo = "",
                    listOtherInputClientErrorCode = null,
                ) to null
            }

            is ForgotPasswordViewEvent.OnInputContactInfoChangeEvent -> {
                previousState.copy(contactInfo = event.contactInfo) to ForgotPasswordViewEffect.ValidPhoneNumberEffect(
                    phoneNumber = event.contactInfo
                )
            }

            is ForgotPasswordViewEvent.OnInputRegistrationNumberChangeEvent -> {
                previousState.copy(registrationNumber = event.registrationNumber) to null
            }

            is ForgotPasswordViewEvent.OnInputContactInfoValidEvent -> {
                val newEffect = if (previousState.methodOtp == MethodOtp.SMS) {
                    ForgotPasswordViewEffect.ValidPhoneNumberEffect(phoneNumber = previousState.contactInfo)
                } else {
                    ForgotPasswordViewEffect.ValidEmailEffect(email = previousState.contactInfo)
                }
                previousState to newEffect
            }

            is ForgotPasswordViewEvent.ValidOtherInputDoneFail -> {
                previousState.copy(
                    listOtherInputClientErrorCode = event.listClientErrorCode
                ) to null
            }

            is ForgotPasswordViewEvent.ValidOtherInputDoneSuccess -> {
                previousState.copy(
                    listOtherInputClientErrorCode = null
                ) to ForgotPasswordViewEffect.SubmitDataEffect(
                    username = previousState.username,
                    idNumber = previousState.idNumber,
                    expirationDate = previousState.expirationDate,
                    methodOtp = previousState.methodOtp,
                    contactInfo = previousState.contactInfo,
                    registrationNumber = previousState.registrationNumber,
                )
            }

            is ForgotPasswordViewEvent.SubmitDataSuccessEvent -> {
                val isAdminRole =
                    event.data?.userRole?.contains(Constants.ADMIN_ROLE, ignoreCase = true) == true
                if (isAdminRole) {
                    ForgotPasswordViewState() to ForgotPasswordViewEffect.GoToVerifyQuestionEffect(
                        username = previousState.username,
                        txnId = event.data?.basicOtp?.txnId,
                        listQuestions = event.data?.securityQuestions ?: emptyList()
                    )
                } else {
                    previousState to ForgotPasswordViewEffect.GoToSmsOtpEffect(
                        data = event.data,
                        username = previousState.username
                    )
                }
            }

            is ForgotPasswordViewEvent.ValidateDataEvent -> {
                previousState to ForgotPasswordViewEffect.ValidateDataEffect(
                    username = previousState.username,
                    idNumber = previousState.idNumber,
                    expirationDate = previousState.expirationDate,
                    methodOtp = previousState.methodOtp,
                    contactInfo = previousState.contactInfo,
                    registrationNumber = previousState.registrationNumber,
                    isIndefinite = previousState.isIndefinite
                )
            }

            is ForgotPasswordViewEvent.ValidEmailOrPhoneNumberInputDoneSuccess -> {
                previousState.copy(listOtherInputClientErrorCode = null) to null
            }

            is ForgotPasswordViewEvent.OnInputIsIndefiniteChangeEvent -> {
                previousState.copy(
                    isIndefinite = event.isIndefinite,
                    expirationDate = "",
                    listOtherInputClientErrorCode = null,
                ) to null
            }
        }
    }
}
