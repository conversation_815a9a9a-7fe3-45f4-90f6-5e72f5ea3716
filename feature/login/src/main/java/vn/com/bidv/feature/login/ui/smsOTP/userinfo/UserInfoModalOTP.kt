@file:JvmName("UserInfoModalOTPViewModelKt")

package vn.com.bidv.feature.login.ui.smsOTP.userinfo

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.ui.BaseDialogScreen
import vn.com.bidv.feature.login.domain.VerifyPwOtpErrorType
import vn.com.bidv.feature.login.domain.model.ModelSmsOTPConfigDMO
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPContent
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.localization.R as RLocalization

@Composable
fun UserInfoModalOTP(
    navController: NavHostController,
    modelConfig: ModelSmsOTPConfigDMO,
) {
    val viewModel: UserInfoModalOTPViewModel = hiltViewModel()
    var isShowErrorPopup by remember { mutableStateOf(false) }
    var isShowErrorMessage by remember { mutableStateOf("") }
    var isBackToSetting by remember { mutableStateOf(false) }
    BaseDialogScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.initSuccess) {
                onEvent(
                    BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.InitData(
                        modelConfig
                    )
                )
            }
            BaseIBankModalOTPContent(
                uiState = uiState,
                onEvent = onEvent,
                viewModel = viewModel
            )
            if (isShowErrorPopup) {
                IBankModalConfirm(
                    title = stringResource(RLocalization.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = isShowErrorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(RLocalization.string.close),
                        )
                    ),
                    onDismissRequest = {
                        isShowErrorPopup = false
                        if (isBackToSetting) {
                            navController.popBackStack(
                                IBankMainRouting.AuthRoutes.UserInfoRoute.route,
                                inclusive = true
                            )
                        } else {
                            onEvent(BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.ClearOTP)
                        }
                    }
                )
            }

        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPError -> {
                    isShowErrorPopup = true
                    isShowErrorMessage =
                        (sideEffect.errorMessage ?: navController.context.getString(
                            RLocalization.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                        ))
                    isBackToSetting = sideEffect.errorCode == VerifyPwOtpErrorType.SMS_OTP_BLOCK
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTPSuccess<UserPersonalInfoDMO> -> {
                    navController.popBackStack()
                }

                is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.OnDismiss -> {
                    navController.popBackStack()
                }

                else -> {
                    // no thing
                }
            }

        }
    )
}