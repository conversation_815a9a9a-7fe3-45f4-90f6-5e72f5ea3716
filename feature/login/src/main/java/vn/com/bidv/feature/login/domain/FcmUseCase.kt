package vn.com.bidv.feature.login.domain

import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await
import javax.inject.Inject

class FcmUseCase @Inject constructor(
) {
    suspend fun getFcmId(): String {
        val fcmId = try {
            FirebaseMessaging.getInstance().token.await()
        } catch (e: Exception) {
            ""
        }
        return fcmId
    }
}