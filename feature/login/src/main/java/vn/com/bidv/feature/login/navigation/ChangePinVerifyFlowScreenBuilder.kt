package vn.com.bidv.feature.login.navigation

import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCaseDefault
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import javax.inject.Inject

class ChangePinVerifyFlowScreenBuilder @Inject constructor(
    useCase: VerifyByTypeTransactionUseCaseDefault,
): VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.CHANGE_PIN,
    isAllowTransResponse = true
)