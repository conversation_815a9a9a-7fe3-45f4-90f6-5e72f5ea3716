import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.localization.R

@Composable
fun OpenBrowser(url: String) {
    val context = LocalContext.current
    val intent = remember { Intent(Intent.ACTION_VIEW, Uri.parse(url)) }
    var isShowPopup by remember { mutableStateOf(true) }
    try {
        context.startActivity(intent)
    } catch (e: ActivityNotFoundException) {
        if (isShowPopup) {
            IBankModalConfirm(
                modalConfirmType = ModalConfirmType.Warning,
                title = stringResource(R.string.loi),
                supportingText = stringResource(R.string.khong_tim_thay_ung_dung_trinh_duyet_nao_de_mo_url),
                onDismissRequest = { isShowPopup = false },
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.dong),
                    ),
                )
            )
        }
    }
}