package vn.com.bidv.sdkbase.utils

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import coil.ImageLoader
import coil.request.ImageRequest
import coil.request.SuccessResult
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.data.NotificationData
import vn.com.bidv.sdkbase.navigation.notifyroute.NotiParam
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants

object NotificationUtils {
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO

    fun getNotificationData(notificationData: String): NotificationData? {
        return try {
            val notificationObject = JSONObject(notificationData)
            val navigateId = notificationObject.optString(
                NotificationConstants.REDIRECT_ID,
                NotificationConstants.BLANK
            )
            val notifyId = notificationObject.optLong(
                NotificationConstants.NOTIFY_ID, -1
            )
            val notiType = notificationObject.optString(
                NotificationConstants.NOTI_TYPE,
                NotificationConstants.BLANK
            )
            val displayTab = notificationObject.optString(
                NotificationConstants.DISPLAY_TAB,
                NotificationConstants.BLANK
            )
            val paramsObject = notificationObject.optJSONObject(
                NotificationConstants.PARAMS
            )
            var notificationDataRes = NotificationData(
                navigateId = navigateId,
                notifyId = notifyId,
                notiType = notiType,
                displayTab = displayTab
            )
            paramsObject?.let {
                val listParam = paramsObject.keys().asSequence()
                    .map { key -> NotiParam(key, paramsObject.getString(key)) }
                    .toList()
                notificationDataRes = notificationDataRes.copy(
                    listParam = listParam
                )
            }

            notificationDataRes
        } catch (e: Exception) {
            BLogUtil.e("getNotificationData error: ${e.message}")
            NotificationData("-1")
        }
    }

    fun makeNotificationIntent(
        context: Context,
        notificationData: String
    ): Intent {
        val activityClazz = Class.forName(NotificationConstants.MAIN_ACTIVITY_CLASS)
        val notyIntent = Intent(context, activityClazz)
        notyIntent.putExtra(NotificationConstants.MORE_INFO, notificationData)
        return notyIntent
    }

    suspend fun loadBitmapWithCoil(
        dispatcherBlock: CoroutineDispatcher = this.ioDispatcher,
        context: Context,
        imageLoader: ImageLoader,
        imageUrl: String?
    ): Bitmap? {
        if (imageUrl.isNullOrEmpty()) return null
        return withContext(dispatcherBlock) {
            try {
                val request = ImageRequest.Builder(context)
                    .data(imageUrl)
                    .crossfade(true)
                    .build()

                val result = (imageLoader.execute(request) as? SuccessResult)?.drawable
                (result as? BitmapDrawable)?.bitmap
            } catch (e: Exception) {
                BLogUtil.e("loadBitmapWithCoil error ${e.message}")
                null
            }
        }
    }

    fun clearAllNotifications(context: Context) {
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
    }

}