package vn.com.bidv.sdkbase.utils

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.domain.DomainResult
import java.lang.reflect.Type
import java.math.BigDecimal

fun Int?.orDefault(default: Int = 0): Int = this ?: default

fun Double?.orDefault(default: Double = 0.0): Double = this ?: default

fun Float?.orDefault(default: Float = 0f): Float = this ?: default

fun Long?.orDefault(default: Long = 0L): Long = this ?: default

fun Short?.orDefault(default: Short = 0): Short = this ?: default

fun Byte?.orDefault(default: Byte = 0): Byte = this ?: default

fun Boolean?.orDefault(default: Boolean = false): Boolean = this ?: default

fun <T> genericTypeToken(): TypeToken<T> = object : TypeToken<T>() {}

@Throws(JsonSyntaxException::class)
fun <T> Any?.mapTo(
    cls: Class<T>, proceed: (T.() -> Unit)? = null
): T {
    try {
        val gson = Gson()
        val data = gson.toJson(this)
        val result = gson.fromJson(data, cls)
        if (proceed != null) {
            result.proceed()
        }
        return result
    } catch (e: JsonSyntaxException) {
        throw e
    }
}

@Throws(JsonSyntaxException::class)
fun <T> Any?.mapTo(
    typeToken: TypeToken<T>, proceed: (T.() -> Unit)? = null
): T {
    try {
        val gson = Gson()
        val data = gson.toJson(this)
        val result = gson.fromJson(data, typeToken)
        if (proceed != null) {
            result.proceed()
        }
        return result
    } catch (e: JsonSyntaxException) {
        throw e
    }
}

fun <F, T> NetworkResult<F>.convert(
    cls: Class<T>, proceed: ((from: F?, to: T?) -> T?)? = null
): DomainResult<T> {
    val result = when (this) {
        is NetworkResult.Success -> {
            try {
                val gson = Gson()
                val jsonData = gson.toJson(data)
                val result = gson.fromJson(jsonData, cls)
                val output = proceed?.invoke(data, result) ?: result
                DomainResult.Success(output)
            } catch (e: Exception) {
                BLogUtil.logException(e)
                DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
            }
        }

        is NetworkResult.Error -> {
            try {
                val gson = Gson()
                val jsonData = gson.toJson(data)
                val result = gson.fromJson(jsonData, cls)
                val output = proceed?.invoke(data, result) ?: result
                DomainResult.Error(
                    errorCode = errorCode,
                    errorMessage = errorMessage,
                    data = output?.let {
                        Gson().toJsonTree(it)
                    },
                    status = status
                )
            } catch (e: Exception) {
                BLogUtil.logException(e)
                DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
            }
        }

        else -> {
            DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
        }
    }
    return result
}

fun <F, T> NetworkResult<F>.convert(
    convert: F.() -> T
): DomainResult<T> {
    try {
        val result = when (this) {
            is NetworkResult.Success -> {
                try {
                    val dataResult = data?.convert()
                    DomainResult.Success(dataResult)
                } catch (e: Exception) {
                    e.printStackTrace()
                    DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
                }
            }

            is NetworkResult.Error -> {
                DomainResult.Error(
                    errorCode = errorCode,
                    errorMessage = errorMessage,
                    data = data?.convert()?.let {
                        Gson().toJsonTree(it)
                    },
                    status = status
                )
            }

            else -> {
                DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
            }
        }
        return result
    } catch (e: Exception) {
        e.printStackTrace()
        return DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
    }
}

fun <F, T> NetworkResult<F>.convert(
    typeOfT: Type,
    proceed: ((from: F?, to: T?) -> T?)? = null
): DomainResult<T> {
    val result = when (this) {
        is NetworkResult.Success -> {
            try {
                val gson = Gson()
                val jsonData = gson.toJson(data)
                val result: T = gson.fromJson(jsonData, typeOfT)
                val output = proceed?.invoke(data, result) ?: result
                DomainResult.Success(output)
            } catch (e: Exception) {
                BLogUtil.logException(e)
                DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
            }
        }

        is NetworkResult.Error -> {
            try {
                val gson = Gson()
                val jsonData = gson.toJson(data)
                val result: T = gson.fromJson(jsonData, typeOfT)
                val output = proceed?.invoke(data, result) ?: result
                DomainResult.Error(
                    errorCode = errorCode,
                    errorMessage = errorMessage,
                    data = output?.let { Gson().toJsonTree(it) },
                    status = status
                )
            } catch (e: Exception) {
                BLogUtil.logException(e)
                DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
            }
        }

        else -> {
            DomainResult.Error(DomainErrorCode.PARSE_DATA_ERROR)
        }
    }
    return result
}


fun String?.toSafeBigDecimal(defaultValue: BigDecimal = BigDecimal.ZERO): BigDecimal {
    return this?.let {
        try {
            BigDecimal(it)
        } catch (e: NumberFormatException) {
            defaultValue
        }
    } ?: defaultValue
}

fun String?.toSafeInt(defaultValue: Int = 0): Int {
    return this?.toIntOrNull() ?: defaultValue
}

fun BigDecimal.isGreaterThan(other: BigDecimal): Boolean {
    return this > other
}

fun String?.toSafeNullBigDecimal(): BigDecimal? {
    return this?.let {
        try {
            BigDecimal(it)
        } catch (e: NumberFormatException) {
            null
        }
    }
}