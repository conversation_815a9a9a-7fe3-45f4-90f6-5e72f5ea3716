package vn.com.bidv.sdkbase.component.listWithLoadMore

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.collectLatest

@Composable
fun <T> ListViewLoadMore(
    itemLoadMoreIndicator: @Composable () -> Unit,
    errorView: @Composable (errorMessage: String?) -> Unit,
    itemContent: @Composable (T) -> Unit,
    loadMoreListInterface: LoadMoreListInterface<T>,
    listState: LazyListState = rememberLazyListState(),
    modifier: Modifier = Modifier,
    userScrollEnabled: Boolean = true,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    itemKey: ((Int) -> Any)? = { it },
) {
    val loadMoreState = loadMoreListInterface.loadMoreState.collectAsState()
    LazyColumn(
        state = listState,
        modifier = modifier,
        userScrollEnabled = userScrollEnabled,
        contentPadding = contentPadding
    ) {
        items(
            count = loadMoreState.value.listDataLoaded.size,
            key = itemKey
        ) { index ->
            itemContent(loadMoreState.value.listDataLoaded[index])
        }
        when(val state = loadMoreState.value) {
            is LoadMoreListState.Error -> {
                item {
                    errorView(state.message)
                }
            }
            is LoadMoreListState.Loading -> {
                item {
                    itemLoadMoreIndicator()
                }
            }
            is LoadMoreListState.Complete -> Unit
        }
    }

    LaunchedEffect(listState) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
            .collectLatest { lastIndex ->
                if (lastIndex != null && lastIndex >= loadMoreState.value.listDataLoaded.size - 1) {
                    loadMoreListInterface.loadMoreData()
                }
            }
    }
}