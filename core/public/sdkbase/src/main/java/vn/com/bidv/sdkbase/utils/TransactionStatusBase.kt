package vn.com.bidv.sdkbase.utils

import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil

sealed class TransactionStatusBase(
    val statusCode: String, val color: LabelColor, val statusResourceId: Int
) {
    data object UNKNOWN : TransactionStatusBase("UNKNOWN", LabelColor.ERROR, R.string.khong_xac_dinh_trang_thai)

    data object INIT : TransactionStatusBase("INIT", LabelColor.LIGHT_GRAY, R.string.tao_moi)

    data object REJECTED :
        TransactionStatusBase("REJECTED", LabelColor.ERROR, R.string.tu_choi_duyet)

    data object PENDING_APPROVAL :
        TransactionStatusBase("PENDING_APPROVAL", LabelColor.BRAND, R.string.cho_duyet)

    data object APPROVED : TransactionStatusBase("APPROVED", LabelColor.BRAND, R.string.da_duyet)

    data object FUTURE :
        TransactionStatusBase("FUTURE", LabelColor.BRAND, R.string.hieu_luc_tuong_lai)

    data object BANK_PROCESSING :
        TransactionStatusBase("BANK_PROCESSING", LabelColor.BRAND, R.string.ngan_hang_xu_ly)

    data object SUCCESS : TransactionStatusBase("SUCCESS", LabelColor.LIGHT_GRAY, R.string.thanh_cong)

    data object FAILED :
        TransactionStatusBase("FAILED", LabelColor.ERROR, R.string.khong_thanh_cong)

    data object UNDEFINED :
        TransactionStatusBase("UNDEFINED", LabelColor.ERROR, R.string.chua_xac_dinh)

    data object DELETED : TransactionStatusBase("DELETED", LabelColor.LIGHT_GRAY, R.string.da_xoa)

    data object CANCELLED :
        TransactionStatusBase("CANCELLED", LabelColor.LIGHT_GRAY, R.string.het_hieu_luc)

    companion object {
        fun fromString(status: String): TransactionStatusBase {
            return try {
                when (status.uppercase()) {
                    "INIT" -> INIT
                    "REJECTED" -> REJECTED
                    "PENDING_APPROVAL" -> PENDING_APPROVAL
                    "APPROVED" -> APPROVED
                    "FUTURE" -> FUTURE
                    "BANK_PROCESSING" -> BANK_PROCESSING
                    "SUCCESS" -> SUCCESS
                    "FAILED" -> FAILED
                    "UNDEFINED" -> UNDEFINED
                    "DELETED" -> DELETED
                    "CANCELLED" -> CANCELLED
                    else -> throw IllegalArgumentException("Invalid status: $status")
                }
            } catch (e: IllegalArgumentException) {
                BLogUtil.e("TransactionStatusBase: ${e.message}")
                UNKNOWN
            }
        }
    }
}

sealed class CNRTransactionStatus(
    val statusCode: String, val color: LabelColor, val statusResourceId: Int
) {
    data class TransactionStatus(val status: TransactionStatusBase) :
        CNRTransactionStatus(status.statusCode, status.color, status.statusResourceId) {
        override fun toString(): String {
            return status.toString()
        }
    }

    data object SUCCESS_PART :
        CNRTransactionStatus("PARTIALLY_SUCCESS", LabelColor.LIGHT_GRAY, R.string.thanh_cong_mot_phan)

    data object ALL :
        CNRTransactionStatus("ALL", LabelColor.BRAND, R.string.tat_ca)

    companion object {
        fun fromString(status: String): CNRTransactionStatus {
            return when (status.uppercase()) {
                "PARTIALLY_SUCCESS" -> SUCCESS_PART
                else -> {
                    val baseStatus = TransactionStatusBase.fromString(status)
                    TransactionStatus(baseStatus)
                }
            }
        }
    }
}

sealed class LoanTransactionStatus(
    val statusCode: String,
    val color: LabelColor,
    val statusResourceId: Int,
) {
    data class TransactionStatus(
        val status: TransactionStatusBase,
    ) : LoanTransactionStatus(status.statusCode, status.color, status.statusResourceId) {
        override fun toString(): String = status.toString()
    }

    data object WAITING_SIGN :
        LoanTransactionStatus("WAITING_SIGN", LabelColor.BRAND, R.string.cho_ky_so_to_chuc)

    companion object {
        fun fromString(status: String): LoanTransactionStatus =
            when (status.uppercase()) {
                "WAITING_SIGN" -> WAITING_SIGN
                else -> {
                    val baseStatus = TransactionStatusBase.fromString(status)
                    TransactionStatus(baseStatus)
                }
            }
    }
}

sealed class TransactionMethodBase(
    val methodCode: String, val statusResourceId: Int
) {
    data object UNKNOWN : TransactionMethodBase("", 0)
    data object INSIDE : TransactionMethodBase("I", R.string.chuyen_tien_trong_bidv)
    data object OUTSIDE : TransactionMethodBase("O", R.string.chuyen_tien_ngoai_bidv)
    data object FAST :
        TransactionMethodBase("F", R.string.chuyen_tien_nhanh_247)
}

sealed class FXTransactionStatus(
    val statusCode: String, val color: LabelColor, val statusResourceId: Int
) {
    data class TransactionStatus(val status: TransactionStatusBase) :
        FXTransactionStatus(status.statusCode, status.color, status.statusResourceId) {
        override fun toString(): String {
            return status.toString()
        }
    }

    data object PARTIALLY_SUCCESS :
        FXTransactionStatus(
            "PARTIALLY_SUCCESS", LabelColor.LIGHT_GRAY, R.string.thanh_cong_mot_phan
        )

    data object APPROVED :
        FXTransactionStatus("APPROVED", LabelColor.BRAND, R.string.cho_thanh_toan)

    companion object {
        fun fromString(status: String): FXTransactionStatus {
            return when (status.uppercase()) {
                "PARTIALLY_SUCCESS" -> PARTIALLY_SUCCESS
                "APPROVED" -> APPROVED
                else -> {
                    val baseStatus = TransactionStatusBase.fromString(status)
                    TransactionStatus(baseStatus)
                }
            }
        }

        fun getTransactionStatusList(): List<FXTransactionStatus> {
            val baseStatuses = listOf(
                TransactionStatus(TransactionStatusBase.INIT),
                TransactionStatus(TransactionStatusBase.REJECTED),
                TransactionStatus(TransactionStatusBase.PENDING_APPROVAL),
                TransactionStatus(TransactionStatusBase.FUTURE),
                TransactionStatus(TransactionStatusBase.BANK_PROCESSING),
                TransactionStatus(TransactionStatusBase.SUCCESS),
                TransactionStatus(TransactionStatusBase.FAILED),
                TransactionStatus(TransactionStatusBase.UNDEFINED),
                TransactionStatus(TransactionStatusBase.DELETED),
                TransactionStatus(TransactionStatusBase.CANCELLED)
            )
            return baseStatuses + listOf(
                APPROVED,
                PARTIALLY_SUCCESS,
            )
        }

        fun getTransactionStatusPendingList(): List<FXTransactionStatus> {
            return listOf(
                TransactionStatus(TransactionStatusBase.INIT),
                TransactionStatus(TransactionStatusBase.REJECTED),
            )
        }
    }
}
