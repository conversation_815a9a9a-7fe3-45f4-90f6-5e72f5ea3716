package vn.com.bidv.sdkbase.component.screen

import android.app.Activity
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.core.view.WindowInsetsControllerCompat
import vn.com.bidv.common.ui.IBankScreenState
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.localization.R

@Composable
fun <T> IBankScreenWithLoadData(
    isLightStatusBar: Boolean = true,
    iBankTopAppBar: @Composable () -> Unit = {},
    viewModel: IBankScreenWithLoadingViewModel<T>,
    loadingView: @Composable () -> Unit = { IBankLoaderIndicators() },
    errorView: @Composable (String?) -> Unit = {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = it,
            textButton = stringResource(id = R.string.retry),
            onClickButton = { viewModel.loadScreenData() }
        )
    },
    contentView: @Composable (T?) -> Unit,
) {

    IBankScreen(
        isLightStatusBar = isLightStatusBar,
        iBankTopAppBar = iBankTopAppBar,
    ) {
        val screenState = viewModel.screenState.collectAsState()
        when (val state = screenState.value) {
            is IBankScreenState.Initial -> {
                viewModel.loadScreenData()
                loadingView.invoke()
            }

            is IBankScreenState.Loading -> {
                loadingView.invoke()
            }

            is IBankScreenState.Error -> {
                errorView.invoke(state.message)
            }

            is IBankScreenState.Success -> {
                contentView(state.data)
            }
        }
    }
}


@Composable
fun IBankScreen(
    isLightStatusBar: Boolean = true,
    iBankTopAppBar: @Composable () -> Unit = {},
    content: @Composable () -> Unit
) {
    val activity = LocalContext.current as? Activity
    activity?.window?.let {
        val controller = WindowInsetsControllerCompat(it, it.decorView)
        controller.isAppearanceLightStatusBars = isLightStatusBar
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        iBankTopAppBar()
        content()
    }
}