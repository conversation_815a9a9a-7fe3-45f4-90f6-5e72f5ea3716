plugins {
    alias(libs.plugins.local.android.library)
    alias(libs.plugins.local.android.compose)
    alias(libs.plugins.local.generate.figma)
}

android {
    namespace = "vn.com.bidv.designsystem"
}

figmaConfig {
    jsonFileName = "figma.json"
}

dependencies {
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.metrics)
    implementation(libs.androidx.tracing.ktx)
    implementation(libs.lottie)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(project(":core:public:localization"))

    debugApi(libs.androidx.compose.ui.tooling)
    implementation(libs.androidx.compose.ui.tooling)
    implementation(libs.core.common)
    implementation(libs.coil.compose)
    implementation(libs.coil.network)
    implementation(libs.richeditor.compose)

}
