package vn.com.bidv.designsystem.component.datepicker

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datepicker.model.CalendarMode
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.component.datepicker.model.PickerType
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.util.testTagIBank
import java.util.Date
import vn.com.bidv.localization.R as RLocalization

/***
 * @param dateSelected is default dateSelected
 * @param config is config for date picker
 * @param onDismissRequest is callback when dismiss date picker.
 * dateSelected is null if not choose date
 * */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IBankDatePickerDialog(
    modifier: Modifier,
    title: String,
    negativeButtonText: String = stringResource(RLocalization.string.xoa_bo_loc),
    positiveButtonText: String = stringResource(RLocalization.string.xac_nhan),
    dateSelected: Date?,
    config: DatePickerConfig = DatePickerConfig.build(),
    onDismissRequest: () -> Unit = { },
    closeIcon: ImageVector? = ImageVector.vectorResource(id = R.drawable.close_outline),
    onNegativeAction: () -> Unit = {},
    onDateSelected: (dateSelected: Date?) -> Unit,
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    var dialogTitle by remember { mutableStateOf(title) }
    IBankBottomSheet(
        title = dialogTitle,
        closeIcon = closeIcon,
        onDismiss = {
            onDismissRequest()
        },
        sheetState = sheetState,
        dragHandle = null,
    ) {
        IBankCalendarPicker(
            modifier = modifier.testTagIBank("IBankDatePickerDialog_$title"),
            start = dateSelected,
            end = null,
            mode = CalendarMode.SINGLE,
            negativeButtonText = negativeButtonText,
            positiveButtonText = positiveButtonText,
            config = config,
            onPickerTypeStateChange = {
                dialogTitle = when (it) {
                    PickerType.DAY -> title
                    PickerType.MONTH -> context.getString(RLocalization.string.chon_thang)
                    PickerType.YEAR -> context.getString(RLocalization.string.chon_nam)
                }
            },
            onDateSelected = { selectedDate ->
                onDateSelected(selectedDate)
                scope.launch {
                    sheetState.hide()
                    onDismissRequest()
                }
            },
            onDateRangeSelected = { _, _ -> },
            onCancel = onNegativeAction,
        )
    }
}

/***
 * @param startDateSelected is default start date selected
 * @param endDateSelected is default end date selected
 * @param config is config for date picker
 * @param onSubmitAction is callback when dismiss date picker dialog.
 * from, to is null if not choose date
 * */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IBankDateRangePickerDialog(
    modifier: Modifier,
    title: String,
    negativeButtonText: String = stringResource(RLocalization.string.xoa_bo_loc),
    positiveButtonText: String = stringResource(RLocalization.string.xac_nhan),
    startDateSelected: Date?,
    endDateSelected: Date?,
    config: DatePickerConfig = DatePickerConfig.build { },
    onDismissRequest: () -> Unit = { },
    onNegativeAction: () -> Unit = {},
    onDateRangeSelected: (from: Date?, to: Date?) -> Unit,
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    var dialogTitle by remember { mutableStateOf(title) }
    IBankBottomSheet(
        title = dialogTitle,
        closeIcon = ImageVector.vectorResource(id = R.drawable.close_outline),
        onDismiss = {
            onDismissRequest()
        },
        sheetState = sheetState,
        dragHandle = null,
    ) {
        IBankCalendarPicker(
            modifier = modifier.testTagIBank("IBankDateRangePickerDialog_$title"),
            start = startDateSelected,
            end = endDateSelected,
            mode = CalendarMode.RANGE,
            negativeButtonText = negativeButtonText,
            positiveButtonText = positiveButtonText,
            config = config,
            onPickerTypeStateChange = {
                dialogTitle = when (it) {
                    PickerType.DAY -> title
                    PickerType.MONTH -> context.getString(RLocalization.string.chon_thang)
                    PickerType.YEAR -> context.getString(RLocalization.string.chon_nam)
                }
            },
            onDateSelected = {},
            onDateRangeSelected = { from, to ->
                onDateRangeSelected(from, to)
                scope.launch {
                    sheetState.hide()
                    onDismissRequest()
                }
            },
            onCancel = onNegativeAction,
        )
    }
}