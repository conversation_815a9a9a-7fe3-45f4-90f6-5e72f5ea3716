package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A composable function for creating an input field specifically designed for money input in an IBank application.
 *
 * @param required Indicates whether the input field is required. Defaults to `false`.
 * @param placeholderText A placeholder text displayed when the field is empty. Defaults to an empty string.
 * @param textValue The current value of the input field, represented as a [TextFieldValue]. Defaults to an empty value.
 * @param maxLengthText Maximum length of the input text. If `null`, no restriction is applied. Defaults to `null`.
 * @param filters A list of [IBankFilterTextField] objects to apply custom filters to the input. Defaults to an empty list.
 * @param currencyCode A string representing the currency code (e.g., "VND", "USD"). Defaults to "VND".
 * @param textStyle The style applied to the text inside the input field. Defaults to `LocalTypography.current.bodyBody_l`.
 * @param showIconHelp Determines whether a help icon should be displayed. Defaults to `false`.
 * @param iconEnd An optional composable lambda function to define a custom icon at the end of the input field. Defaults to `null`.
 * @param state Represents the visual state of the input field (e.g., error, default). Defaults to `IBFrameState.DEFAULT`.
 * @param helpTextLeft The text displayed on the left side of the help section. Defaults to an empty string.
 * @param helpTextRight The text displayed on the right side of the help section. Defaults to an empty string.
 * @param onClickHelp A callback function triggered when the help icon is clicked. Defaults to an empty lambda.
 * @param onClickClear A callback function triggered when the clear icon is clicked. Defaults to an empty lambda.
 * @param onValueChange A lambda function triggered when the input value changes. It takes a [TextFieldValue] as input.
 * @param onSelectCurrency A callback function triggered when the currency selector is clicked.
 */

@Composable
fun IBankInputMoney(
    required: Boolean? = false,
    placeholderText: String = "",
    textValue: String = "",
    maxLengthText: Int? = null,
    filters: MutableList<IBankFilterTextField> = mutableListOf(),
    currencyCode: String = "VND",
    pattern: FormatCurrency.Pattern = FormatCurrency.Pattern.VND,
    textStyle: TextStyle = LocalTypography.current.bodyBody_l,
    showIconHelp: Boolean = false,
    iconEnd: @Composable (() -> Unit)? = null,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    onClickHelp: () -> Unit? = {},
    onClickClear: () -> Unit = {},
    onAmountChange: (String) -> Unit = {},
    onFocusChange: (Boolean) -> Unit = {},
    showCurrency: Boolean = false,
    showDropdownCurrency: Boolean = false,
    onSelectCurrency: () -> Unit,
) {
    val defaultFilter = mutableListOf<IBankFilterTextField>()
    defaultFilter.add(InputFieldMoneyFilter())
    defaultFilter.add(FormatCurrency(pattern, maxLengthText))
    defaultFilter.addAll(filters)
    Column {
        IBankInputMoneyBody(
            required = required,
            placeholderText = placeholderText,
            textValue = textValue,
            maxLengthText = null,
            currencyCode = currencyCode,
            filters = defaultFilter,
            textStyle = textStyle,
            showIconHelp = showIconHelp,
            iconEnd = iconEnd,
            state = state,
            helpTextLeft = helpTextLeft,
            helpTextRight = helpTextRight,
            onClickHelp = onClickHelp,
            onClickClear = onClickClear,
            onMoneyChange = onAmountChange,
            onFocusChange = onFocusChange,
            showDropdown = showDropdownCurrency,
            showCurrency = showCurrency,
            onSelectCurrency = onSelectCurrency,
        )
    }
}

@Composable
private fun IBankInputMoneyBody(
    required: Boolean? = false,
    placeholderText: String = "",
    textValue: String? = null,
    maxLengthText: Int? = null,
    isShowMaxLength: Boolean = false,
    currencyCode: String = "VND",
    filters: MutableList<IBankFilterTextField>? = null,
    textStyle: TextStyle = LocalTypography.current.bodyBody_l,
    showIconHelp: Boolean = false,
    iconEnd: @Composable (() -> Unit)? = null,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    onClickHelp: () -> Unit? = {},
    onClickClear: () -> Unit = {},
    onMoneyChange: (String) -> Unit = {},
    onFocusChange: (Boolean) -> Unit = {},
    showCurrency: Boolean = false,
    showDropdown: Boolean = false,
    onSelectCurrency: () -> Unit,
) {
    var textShow: String by remember { mutableStateOf(textValue ?: "") }
    LaunchedEffect(textValue) {
        val newString = applyFilterText(textValue, filters)
        if (textShow != "$newString.") {
            textShow = newString
        }
    }
    val focusManager = LocalFocusManager.current
    IBankInputFieldBase(
        placeholderText = placeholderText,
        text = textShow,
        maxLengthText = maxLengthText,
        isShowMaxLength = isShowMaxLength,
        filters = filters,
        required = required,
        inputType = KeyboardOptions(keyboardType = KeyboardType.Number),
        showIconHelp = showIconHelp,
        iconEnd = iconEnd,
        state = state,
        helpTextLeft = helpTextLeft,
        helpTextRight = helpTextRight,
        onClickHelp = onClickHelp,
        onClickClear = onClickClear,
        textStyle = textStyle,
        onFocusChange = {
            if (!it) {
                if (textShow.endsWith(".")) {
                    textShow = textShow.dropLast(1)
                }
                onMoneyChange(textShow.replace(",", ""))
            }
            onFocusChange(it)
        },
        onValueChange = {
            textShow = it.text
            val formatAmount = it.text.replace(",", "")
            onMoneyChange(formatAmount)
        },
        onSubmitText = {
            focusManager.clearFocus()
        },
        customTail = if (showCurrency) {
            {
                IBankInputSuffixCurrency(
                    iconRight = painterResource(id = R.drawable.arrow_bottom_outline),
                    labelTail = currencyCode,
                    showIcon = showDropdown,
                    onIconTailClick = {
                        onSelectCurrency()
                    }
                )
            }
        } else null)
}

@Preview
@Composable
fun PreviewMoney() {

    IBankInputMoney(
        placeholderText = "Nhập số tiền",
        required = false,
//        state = IBFrameState.DEFAULT(colorScheme),
        onSelectCurrency = {

        }
    )
}
