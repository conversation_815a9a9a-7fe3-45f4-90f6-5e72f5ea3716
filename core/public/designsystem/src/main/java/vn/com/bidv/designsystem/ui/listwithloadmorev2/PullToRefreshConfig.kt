package vn.com.bidv.designsystem.ui.listwithloadmorev2

import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.IBankLoaderIndicatorsImpl

/**
 * Sử dụng để cấu hình cho #PullToRefreshColumn.
 * @param enabled: Bật tắt pull to refresh. Mặc định là true.
 * @param loadingIndicatorMaxHeight: Chiều cao tối đa của loading indicator view.
 * @param loadingAnimDurationMillis: Thời gian chạy animation ẩn hiện loading
 */
data class PullToRefreshConfig(
    val enabled: Boolean = true,
    val loadingIndicatorMaxHeight: Dp = 80.dp,
    val loadingAnimDurationMillis: Int = 300,
)