package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing.spacingM
import vn.com.bidv.designsystem.theme.IBSpacing.spacingS
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankCardInit(
    modifier: Modifier = Modifier.fillMaxWidth(),
    title: String,
    tag: @Composable () -> Unit = {},
    content: String? = null,
    description: String? = null,
    isShowDropDown: Boolean = true,
    onClick: () -> Unit = {}
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    var iconHeight by remember { mutableStateOf(0) }


    Box(modifier = modifier.testTagIBank("IBankCardInit_topTitle")
        .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
        .wrapContentHeight()
        .background(brush = IBGradient.cardSecondary)
        .clickable { onClick() }) {

        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_background_center),
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .onGloballyPositioned { coordinates ->
                    iconHeight = coordinates.size.height
                },
            contentDescription = "Account Icon",
            tint = Color.Unspecified
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(with(LocalDensity.current) { iconHeight.toDp() })
                .padding(horizontal = spacingM, vertical = spacingS),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    color = colorSchema.contentOn_specialPrimary,
                    text = title,
                    style = typography.titleTitle_m
                )
                Spacer(Modifier.width(spacingXs))
                tag()
                Spacer(modifier = Modifier.weight(1f))
                if (isShowDropDown) {
                    Icon(
                        modifier = Modifier.size(20.dp).testTagIBank("IBankCardInit_dropDownIcon"),
                        painter = painterResource(id = R.drawable.circle_arrow_down_outline),
                        tint = colorSchema.contentOn_specialPrimary,
                        contentDescription = ""
                    )
                }
            }

            Text(
                color = colorSchema.contentOn_specialPrimary.copy(alpha = 0.8f),
                text = content ?: "",
                style = typography.bodyBody_l,
            )

            Text(
                color = colorSchema.contentOn_specialPrimary.copy(alpha = 0.8f),
                text = description ?: "",
                style = typography.bodyBody_m
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ShowPreviewCardInit() {
    Column(
        Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {

        IBankCardInit(
            title = "***********",
            tag = {
                IBankBadgeLabel(
                    badgeColor = LabelColor.ON_BRAND,
                    badgeSize = LabelSize.SM,
                    badgeType = LabelType.ROUNDED,
                    title = "Mặc định"
                )
            },
            content = "2,049,923,745 VND",
            description = "CONG TY TNHH HOA BINH HOA QUA HAI CHIEU THAN QUA HAI CHIEU THAN QUA HA"
        ) {

        }
        Spacer(modifier = Modifier.height(16.dp))

        IBankCardInit(title = "Chọn tài khoản chuyển tiền") {

        }

    }
}