package vn.com.bidv.designsystem.component.card

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.LocalColorScheme

@Composable
fun IBankDashedLine(
    modifier: Modifier = Modifier.fillMaxWidth(),
    color: Color = LocalColorScheme.current.borderMainPrimary.copy(alpha = 0.2f),
    dashWidth: Dp = 5.dp,
    dashGap: Dp = 5.dp,
    strokeWidth: Dp = IBBorderDivider.borderDividerS
) {
    Canvas(modifier = modifier) {

        val strokeWidthPx = strokeWidth.toPx()

        val pathEffect =
            PathEffect.dashPathEffect(floatArrayOf(dashWidth.toPx(), dashGap.toPx()), 0f)

        drawLine(
            color = color,
            start = Offset(0f, size.height / 2),
            end = Offset(size.width, size.height / 2),
            strokeWidth = strokeWidthPx,
            pathEffect = pathEffect
        )
    }
}
