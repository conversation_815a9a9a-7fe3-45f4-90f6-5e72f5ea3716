package vn.com.bidv.designsystem.component.dataentry

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A Composable function that provides a base input field for the iBank application with extensive customization options.
 *
 * @param required Indicates whether the input is mandatory. Defaults to `false`.
 * @param isTextArea Boolean flag to enable a text area style input field. Defaults to `false`.
 * @param maxLengthText Maximum length of the input text. Defaults to 300 characters.
 * @param placeholderText Placeholder text displayed when the input field is empty. Defaults to an empty string.
 * @param filters A list of filters (`IBankFilterTextField`) applied to the input text. Defaults to `null`.
 * @param textStyle Defines the style of the input text. Defaults to the `bodyBody_m` style from `LocalTypography`.
 * @param showIconHelp Boolean flag to display a help icon inside the input field. Defaults to `false`.
 * @param state Represents the visual state of the input field using `IBFrameState`. Defaults to the default state of `LocalColorScheme`.
 * @param helpTextLeft Text displayed on the left side of the help section. Defaults to an empty string.
 * @param helpTextRight Text displayed on the right side of the help section. Defaults to an empty string.
 * @param onClickHelp A callback function triggered when the help icon is clicked. Defaults to an empty lambda.
 * @param onValueChange A callback function invoked when the input text value changes, passing the updated `TextFieldValue`.
 */
@Composable
fun IBankInputPassword(
    required: Boolean? = false,
    text: String = "",
    isTextArea: Boolean = false,
    maxLengthText: Int? = null,
    isShowMaxLength: Boolean = false,
    placeholderText: String = "",
    filters: List<IBankFilterTextField>? = null,
    textStyle: TextStyle = LocalTypography.current.bodyBody_m,
    showIconHelp: Boolean = false,
    state: IBFrameState = IBFrameState.DEFAULT(LocalColorScheme.current),
    helpTextLeft: String = "",
    helpTextRight: String = "",
    onFocusChange: (Boolean) -> Unit = {},
    onClickHelp: () -> Unit? = {},
    onClickClear: () -> Unit = {},
    onSubmitText: (TextFieldValue) -> Unit = {},
    onValueChange: (TextFieldValue) -> Unit,
) {

    var passwordVisibility by remember { mutableStateOf(false) }
    val icon = if (passwordVisibility) {
        R.drawable.eyes_closed_outline
    } else R.drawable.eyes_open_outline

    val colorScheme = LocalColorScheme.current

    IBankInputFieldBase(
        placeholderText = placeholderText,
        maxLengthText = maxLengthText,
        isShowMaxLength = isShowMaxLength,
        filters = filters,
        text = text,
        placeholderColor = if (state is IBFrameState.DISABLE) colorScheme.contentPlaceholder else null,
        required = required,
        inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
        showIconHelp = showIconHelp,
        state = state,
        iconEnd = {
            Icon(
                painter = painterResource(id = icon),
                tint = if (passwordVisibility) {
                    colorScheme.contentMainPrimary
                } else colorScheme.contentMainSecondary,
                contentDescription = "",
                modifier = Modifier
                    .testTagIBank("icon_password")
                    .padding(start = 4.dp)
                    .size(IBSpacing.spacingL)
                    .clickable {
                        passwordVisibility = !passwordVisibility
                    }
            )
        },
        isTextArea = isTextArea,
        helpTextLeft = helpTextLeft,
        helpTextRight = helpTextRight,
        onClickHelp = onClickHelp,
        textStyle = textStyle.copy(color = if (state is IBFrameState.DISABLE) colorScheme.contentPlaceholder else colorScheme.contentMainPrimary),
        onFocusChange = onFocusChange,
        onValueChange = onValueChange,
        onClickClear = onClickClear,
        onSubmitText = onSubmitText,
        visualTransformation = if (passwordVisibility) VisualTransformation.None else PasswordVisualTransformation(),
    )
}

@Preview
@Composable
fun test() {
    Column {
        IBankInputPassword(
            placeholderText = "Password",
            required = false,
            state = IBFrameState.DISABLE(LocalColorScheme.current),
            text = "aaaaaaaa",
            onValueChange = {}
        )
        Spacer(modifier = Modifier.height(16.dp))
        IBankInputPassword(
            placeholderText = "Password",
            required = false,
            onValueChange = {}
        )
    }
}
