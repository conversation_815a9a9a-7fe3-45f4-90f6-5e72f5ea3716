package vn.com.bidv.ibank.data

import vn.com.bidv.ibank.data.synctime.apis.SyncTimeApi
import vn.com.bidv.ibank.data.synctime.model.SmartOtpSyncTimeRequest
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class SyncTimeRepository @Inject constructor(
    private val api: SyncTimeApi
) : BaseRepository() {
    suspend fun syncTime(userId: Long, smToken: String) = launch {
        api.syncTime(
            SmartOtpSyncTimeRequest(
                userId = userId,
                deviceId = "",
                smToken = smToken
            )
        )
    }
}