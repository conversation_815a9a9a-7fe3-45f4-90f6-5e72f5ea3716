package vn.com.bidv.ibank

import android.app.Application
import coil.ImageLoader
import coil.ImageLoaderFactory
import dagger.hilt.android.HiltAndroidApp
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.common.utils.Utils
import vn.com.bidv.log.BLog
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.network.NetworkFactory
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationBuilderUtils
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationRouteBuilder
import vn.com.bidv.secure.secure.biometric.IBiometricSecure
import vn.com.bidv.secure.utils.BiometricUtils
import javax.inject.Inject
import javax.inject.Provider

@HiltAndroidApp
class IBankApplication : Application(), ImageLoaderFactory {
    @Inject
    lateinit var bLog: BLog

    @Inject
    lateinit var iBiometricSecure: IBiometricSecure

    @Inject
    lateinit var networkConfig: NetworkConfig

    @Inject
    lateinit var imageLoader: Provider<ImageLoader>

    @Inject
    lateinit var notificationRouteBuilders: Set<@JvmSuppressWildcards NotificationRouteBuilder>

    override fun onCreate() {
        super.onCreate()
        Storage.initialize(this)
        NetworkFactory.initialize(networkConfig)
        NotificationBuilderUtils.initialize(notificationRouteBuilders)
        BiometricUtils.initialize(BiometricUtils(iBiometricSecure))
        Utils.setFlavor(BuildConfig.FLAVOR)
        initLog()
    }

    private fun initLog() {
        BLogUtil.initialize(BLogUtil(bLog))
        BLogUtil.setDebuggable(BuildConfig.DEBUG)
        BLogUtil.d("IBankApplication onCreate")
    }

    override fun newImageLoader(): ImageLoader = imageLoader.get()


}


