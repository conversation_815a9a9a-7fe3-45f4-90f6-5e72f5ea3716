{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://*************", "description": "Generated server url"}], "paths": {"/utilities/smartotp/user/sync-time/1.0": {"post": {"tags": ["syncTime"], "summary": "Request to Sync time", "description": "Request to Sync time", "operationId": "syncTime", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpSyncTimeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmarOtpTimeRes"}}}}}}}}, "components": {"schemas": {"SmartOtpSyncTimeRequest": {"required": ["deviceId", "smToken", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "description": "user id", "format": "int64"}, "deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "smToken": {"type": "string", "description": "token smartotp", "example": "abc12345"}}, "description": "Request to delete user smartotp"}, "ResultSmarOtpTimeRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SmarOtpTimeRes"}}}, "SmarOtpTimeRes": {"type": "object", "properties": {"time": {"type": "integer", "description": "time", "format": "int64", "example": 123455}}, "description": "Data"}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}}}, "tags": ["syncTime"], "formated": "1"}