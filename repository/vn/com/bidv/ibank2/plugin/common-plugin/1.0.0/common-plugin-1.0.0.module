{"formatVersion": "1.1", "component": {"group": "vn.com.bidv.ibank2.plugin", "module": "common-plugin", "version": "1.0.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.6"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "files": [{"name": "common-plugin-1.0.0.jar", "url": "common-plugin-1.0.0.jar", "size": 294598, "sha512": "c9feccd94dfa332101749eeb7173474fad75d1c162063af4fbcf8b3654d5a276a2a0d4fdae80437ec42afadc1ff82f8452203a98f2b68245b6d9b728be5a76cc", "sha256": "9fc1a513dfb9076a3fa34c623275837c709e248b033de53537ed2f9116739d70", "sha1": "ecc2bca5ac4bb9201f388b4329e3a733edd543ec", "md5": "c4c1295abeb6037bf85285cc66afe259"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.squareup", "module": "javapoet", "version": {"requires": "1.13.0"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.10"}}, {"group": "org.openapitools", "module": "openapi-generator-gradle-plugin", "version": {"requires": "7.7.0"}}, {"group": "org.yaml", "module": "snake<PERSON>l", "version": {"requires": "2.2"}}, {"group": "com.github.spullara.mustache.java", "module": "compiler", "version": {"requires": "0.9.10"}}], "files": [{"name": "common-plugin-1.0.0.jar", "url": "common-plugin-1.0.0.jar", "size": 294598, "sha512": "c9feccd94dfa332101749eeb7173474fad75d1c162063af4fbcf8b3654d5a276a2a0d4fdae80437ec42afadc1ff82f8452203a98f2b68245b6d9b728be5a76cc", "sha256": "9fc1a513dfb9076a3fa34c623275837c709e248b033de53537ed2f9116739d70", "sha1": "ecc2bca5ac4bb9201f388b4329e3a733edd543ec", "md5": "c4c1295abeb6037bf85285cc66afe259"}]}]}