package vn.com.bidv.feature.government.service.domain

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailRes
import vn.com.bidv.feature.government.service.domain.usecase.GetPendingTransactionDetailUseCase
import vn.com.bidv.feature.government.service.ui.screen.pendingtransactiondetail.model.TransactionDetailDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult

class GetPendingTransactionDetailUseCaseTest {

    private val mockGovernmentServiceRepository: GovernmentServiceRepository = mockk()
    private val useCase = GetPendingTransactionDetailUseCase(mockGovernmentServiceRepository)

    @Test
    fun `invoke should return success result when repository returns success`() = runBlocking {
        // Arrange
        val transactionId = "TX123456789"
        val expectedTxnDetailRes = TxnDetailRes(
            txnId = transactionId,
            debitAccNo = "**********",
            debitAccName = "Test Account",
            taxCode = "**********",
            payerName = "Test Payer",
            payerAddr = "Test Address",
            amount = "1000000",
            amountText = "One million VND",
            ccy = "VND",
            status = "PENDING",
            statusName = "Pending Approval",
            createdDate = "2024-01-15 10:30:00",
            createdBy = "testuser",
            batchNo = "BATCH001",
            treasuryCode = "TREASURY001",
            treasuryName = "Test Treasury",
            admAreaCode = "AREA001",
            admAreaName = "Test Area",
            revAccCode = "REV001",
            revAccName = "Revenue Account",
            revAuthCode = "AUTH001",
            revAuthName = "Test Authority",
            feeTotal = "50000",
            feeOpt = "DEBIT",
            raNote = "Test note to authorizer",
            approvalNote = null,
            tccRefNo = "TCC123456"
        )

        coEvery {
            mockGovernmentServiceRepository.getPendingTransactionDetail(TxnDetailReq(txnId = transactionId))
        } returns NetworkResult.Success(expectedTxnDetailRes)

        // Act
        val result = useCase.invoke(transactionId)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(transactionId, successResult.data?.transactionId)
        assertEquals("**********", successResult.data?.debitAccNumber)
        assertEquals("Test Account", successResult.data?.debitAccName)
        assertEquals("**********", successResult.data?.payerTaxCode)
        assertEquals("Test Payer", successResult.data?.payerName)
        assertEquals("Test Address", successResult.data?.payerAddress)
        assertEquals("1000000", successResult.data?.paymentAmount)
        assertEquals("One million VND", successResult.data?.paymentAmountText)
        assertEquals("VND", successResult.data?.ccy)
        assertEquals("PENDING", successResult.data?.status)
        assertEquals("Pending Approval", successResult.data?.statusName)
    }

    @Test
    fun `invoke should return error result when repository returns error`() = runBlocking {
        // Arrange
        val transactionId = "TX123456789"
        val errorCode = "E001"
        val errorMessage = "Transaction not found"

        coEvery {
            mockGovernmentServiceRepository.getPendingTransactionDetail(TxnDetailReq(txnId = transactionId))
        } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        // Act
        val result = useCase.invoke(transactionId)

        // Assert
        assert(result is DomainResult.Error)
        val errorResult = result as DomainResult.Error
        assertEquals(errorCode, errorResult.errorCode)
        assertEquals(errorMessage, errorResult.errorMessage)
    }

    @Test
    fun `invoke should handle empty transaction id`() = runBlocking {
        // Arrange
        val transactionId = ""
        val expectedTxnDetailRes = TxnDetailRes(
            txnId = transactionId,
            status = "INVALID",
            statusName = "Invalid Transaction"
        )

        coEvery {
            mockGovernmentServiceRepository.getPendingTransactionDetail(TxnDetailReq(txnId = transactionId))
        } returns NetworkResult.Success(expectedTxnDetailRes)

        // Act
        val result = useCase.invoke(transactionId)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(transactionId, successResult.data?.transactionId)
        assertEquals("INVALID", successResult.data?.status)
        assertEquals("Invalid Transaction", successResult.data?.statusName)
    }

    @Test
    fun `invoke should handle transaction with complete tax information`() = runBlocking {
        // Arrange
        val transactionId = "TX987654321"
        val expectedTxnDetailRes = TxnDetailRes(
            txnId = transactionId,
            debitAccNo = "**********",
            debitAccName = "Corporate Account",
            taxCode = "**********",
            altTaxCode = "ALT123456",
            payerName = "Test Corporation",
            altPayerName = "Alternative Payer",
            payerAddr = "123 Business Street",
            altPayerAddr = "456 Alternative Street",
            payerType = 1, // Corporate
            amount = "5000000",
            amountText = "Five million VND",
            ccy = "VND",
            status = "APPROVED",
            statusName = "Approved",
            createdDate = "2024-01-20 14:45:00",
            createdBy = "admin",
            approvedBy = "supervisor",
            batchNo = "BATCH002",
            treasuryCode = "TREASURY002",
            treasuryName = "Main Treasury",
            admAreaCode = "AREA002",
            admAreaName = "Central Area",
            revAccCode = "REV002",
            revAccName = "Main Revenue Account",
            revAuthCode = "AUTH002",
            revAuthName = "Central Authority",
            bbCode = "BB001",
            feeTotal = "100000",
            feeOpt = "CREDIT",
            raNote = "Important transaction",
            approvalNote = "Approved by supervisor",
            tccRefNo = "TCC987654",
            accountingStatus = "POSTED",
            accountingStatusName = "Posted",
            customsConnStatus = "CONNECTED",
            customsConnStatusName = "Connected to Customs"
        )

        coEvery {
            mockGovernmentServiceRepository.getPendingTransactionDetail(TxnDetailReq(txnId = transactionId))
        } returns NetworkResult.Success(expectedTxnDetailRes)

        // Act
        val result = useCase.invoke(transactionId)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(transactionId, successResult.data?.transactionId)
        assertEquals("**********", successResult.data?.debitAccNumber)
        assertEquals("Corporate Account", successResult.data?.debitAccName)
        assertEquals("**********", successResult.data?.payerTaxCode)
        assertEquals("ALT123456", successResult.data?.altTaxCode)
        assertEquals("Test Corporation", successResult.data?.payerName)
        assertEquals("Alternative Payer", successResult.data?.altPayerName)
        assertEquals("123 Business Street", successResult.data?.payerAddress)
        assertEquals("456 Alternative Street", successResult.data?.altPayerAddress)
        assertEquals("5000000", successResult.data?.paymentAmount)
        assertEquals("Five million VND", successResult.data?.paymentAmountText)
        assertEquals("APPROVED", successResult.data?.status)
        assertEquals("Approved", successResult.data?.statusName)
        assertEquals("admin", successResult.data?.createdBy)
        assertEquals("supervisor", successResult.data?.approvedBy)
        assertEquals("POSTED", successResult.data?.accountingStatus)
        assertEquals("Posted", successResult.data?.accountingStatusName)
        assertEquals("CONNECTED", successResult.data?.customsConnStatus)
        assertEquals("Connected to Customs", successResult.data?.customsConnStatusName)
        assertEquals("BB001", successResult.data?.bbCode)
        assertEquals("100000", successResult.data?.feeTotal)
        assertEquals("CREDIT", successResult.data?.feeMethod)
        assertEquals("Important transaction", successResult.data?.noteToAuthorizer)
        assertEquals("Approved by supervisor", successResult.data?.denialReasonNote)
        assertEquals("TCC987654", successResult.data?.tccRefNo)
    }

    @Test
    fun `invoke should handle network timeout error`() = runBlocking {
        // Arrange
        val transactionId = "TX123456789"
        val errorCode = "TIMEOUT"
        val errorMessage = "Request timeout"

        coEvery {
            mockGovernmentServiceRepository.getPendingTransactionDetail(TxnDetailReq(txnId = transactionId))
        } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        // Act
        val result = useCase.invoke(transactionId)

        // Assert
        assert(result is DomainResult.Error)
        val errorResult = result as DomainResult.Error
        assertEquals(errorCode, errorResult.errorCode)
        assertEquals(errorMessage, errorResult.errorMessage)
    }
}
