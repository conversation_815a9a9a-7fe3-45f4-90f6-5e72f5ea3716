package vn.com.bidv.feature.government.service.domain

import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.ResultString
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDeleteReq
import vn.com.bidv.feature.government.service.domain.usecase.DeleteTransactionsUseCase
import vn.com.bidv.feature.government.service.model.TxnDeleteResultDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult

class DeleteTransactionsUseCaseTest {

    private val mockGovernmentServiceRepository: GovernmentServiceRepository = mockk()
    private val useCase = DeleteTransactionsUseCase(mockGovernmentServiceRepository)

    @Test
    fun `invoke should return success result when repository returns success`() = runBlocking {
        // Arrange
        val txnIds = listOf("12345", "67890")
        val expectedCode = null
        val expectedMessage = "Delete successful"

        val resultString = ResultString(
            code = expectedCode,
            message = expectedMessage
        )

        coEvery {
            mockGovernmentServiceRepository.deleteTransaction(TxnDeleteReq(txnIds = txnIds))
        } returns NetworkResult.Success(resultString)

        // Act
        val result = useCase.invoke(txnIds)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(expectedCode, successResult.data?.errorCode)
        assertEquals(expectedMessage, successResult.data?.errorMessage)
        assertEquals(true, successResult.data?.isSuccess())
    }

    @Test
    fun `invoke should return error result when repository returns error`() = runBlocking {
        // Arrange
        val txnIds = listOf("12345", "67890")
        val errorCode = "E001"
        val errorMessage = "Failed to delete transactions"

        coEvery {
            mockGovernmentServiceRepository.deleteTransaction(TxnDeleteReq(txnIds = txnIds))
        } returns NetworkResult.Error(
            errorCode = errorCode,
            errorMessage = errorMessage
        )

        // Act
        val result = useCase.invoke(txnIds)

        // Assert
        assert(result is DomainResult.Error)
        val errorResult = result as DomainResult.Error
        assertEquals(errorCode, errorResult.errorCode)
        assertEquals(errorMessage, errorResult.errorMessage)
    }

    @Test
    fun `invoke should return success with error code when delete operation fails`() = runBlocking {
        // Arrange
        val txnIds = listOf("12345")
        val expectedCode = "E002"
        val expectedMessage = "Transaction not found"

        val resultString = ResultString(
            code = expectedCode,
            message = expectedMessage
        )

        coEvery {
            mockGovernmentServiceRepository.deleteTransaction(TxnDeleteReq(txnIds = txnIds))
        } returns NetworkResult.Success(resultString)

        // Act
        val result = useCase.invoke(txnIds)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(expectedCode, successResult.data?.errorCode)
        assertEquals(expectedMessage, successResult.data?.errorMessage)
        assertEquals(false, successResult.data?.isSuccess())
    }

    @Test
    fun `invoke should handle empty transaction ids list`() = runBlocking {
        // Arrange
        val txnIds = emptyList<String>()
        val expectedCode = null
        val expectedMessage = "Delete successful"

        val resultString = ResultString(
            code = expectedCode,
            message = expectedMessage
        )

        coEvery {
            mockGovernmentServiceRepository.deleteTransaction(TxnDeleteReq(txnIds = txnIds))
        } returns NetworkResult.Success(resultString)

        // Act
        val result = useCase.invoke(txnIds)

        // Assert
        assert(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals(expectedCode, successResult.data?.errorCode)
        assertEquals(expectedMessage, successResult.data?.errorMessage)
        assertEquals(true, successResult.data?.isSuccess())
    }
}