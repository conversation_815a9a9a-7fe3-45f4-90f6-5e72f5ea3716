package vn.com.bidv.feature.government.service.ui.delete

import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Test

class DeleteTransactionsReducerTest {

    private val reducer = DeleteTransactionsReducer()
    private val initialState = DeleteTransactionsReducer.ReducerViewState()

    @Test
    fun `reduce should handle UserRequestDeleteTxns event correctly`() {
        // Arrange
        val txnIds = listOf("TX001", "TX002", "TX003")
        val event = DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(txnIds)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState) // State should remain unchanged
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns)
        val userRequestEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns
        assertEquals(txnIds, userRequestEffect.txnIds)
        assertEquals(3, userRequestEffect.txnIds.size)
        assertEquals("TX001", userRequestEffect.txnIds[0])
        assertEquals("TX002", userRequestEffect.txnIds[1])
        assertEquals("TX003", userRequestEffect.txnIds[2])
    }

    @Test
    fun `reduce should handle UserRequestDeleteTxns with empty list`() {
        // Arrange
        val txnIds = emptyList<String>()
        val event = DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(txnIds)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns)
        val userRequestEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns
        assertEquals(emptyList<String>(), userRequestEffect.txnIds)
        assertEquals(0, userRequestEffect.txnIds.size)
    }

    @Test
    fun `reduce should handle UserRequestDeleteTxns with single transaction`() {
        // Arrange
        val txnIds = listOf("TX123456789")
        val event = DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(txnIds)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns)
        val userRequestEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns
        assertEquals(1, userRequestEffect.txnIds.size)
        assertEquals("TX123456789", userRequestEffect.txnIds[0])
    }

    @Test
    fun `reduce should handle RequestDeleteSuccess event correctly`() {
        // Arrange
        val event = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState) // State should remain unchanged
        assertNotNull(effect)
        assertEquals(DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteSuccess, effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteSuccess)
    }

    @Test
    fun `reduce should handle RequestDeleteFailed event with error details`() {
        // Arrange
        val errorCode = "E001"
        val errorMessage = "Failed to delete transactions"
        val event = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(errorCode, errorMessage)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState) // State should remain unchanged
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed)
        val failedEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed
        assertEquals(errorCode, failedEffect.errorCode)
        assertEquals(errorMessage, failedEffect.errorMessage)
    }

    @Test
    fun `reduce should handle RequestDeleteFailed event with null error details`() {
        // Arrange
        val event = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(null, null)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed)
        val failedEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed
        assertNull(failedEffect.errorCode)
        assertNull(failedEffect.errorMessage)
    }

    @Test
    fun `reduce should handle RequestDeleteFailed event with partial null values`() {
        // Arrange
        val errorCode = "E002"
        val event = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(errorCode, null)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assert(effect is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed)
        val failedEffect = effect as DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed
        assertEquals(errorCode, failedEffect.errorCode)
        assertNull(failedEffect.errorMessage)
    }

    @Test
    fun `ReducerViewState should be instantiable`() {
        // Act
        val state = DeleteTransactionsReducer.ReducerViewState()

        // Assert
        assertNotNull(state)
    }

    @Test
    fun `ReducerViewEvent UserRequestDeleteTxns should hold transaction ids correctly`() {
        // Arrange
        val txnIds = listOf("TX001", "TX002")

        // Act
        val event = DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(txnIds)

        // Assert
        assertEquals(txnIds, event.txnIds)
        assertEquals(2, event.txnIds.size)
    }

    @Test
    fun `ReducerViewEvent RequestDeleteFailed should hold error details correctly`() {
        // Arrange
        val errorCode = "E003"
        val errorMessage = "Network error"

        // Act
        val event = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(errorCode, errorMessage)

        // Assert
        assertEquals(errorCode, event.errorCode)
        assertEquals(errorMessage, event.errorMessage)
    }

    @Test
    fun `ReducerViewEffect UserRequestDeleteTxns should hold transaction ids correctly`() {
        // Arrange
        val txnIds = listOf("TX001", "TX002")

        // Act
        val effect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)

        // Assert
        assertEquals(txnIds, effect.txnIds)
        assertEquals(2, effect.txnIds.size)
    }

    @Test
    fun `ReducerViewEffect RequestDeleteFailed should hold error details correctly`() {
        // Arrange
        val errorCode = "E004"
        val errorMessage = "Validation error"

        // Act
        val effect = DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed(errorCode, errorMessage)

        // Assert
        assertEquals(errorCode, effect.errorCode)
        assertEquals(errorMessage, effect.errorMessage)
    }

    @Test
    fun `reducer should maintain state immutability across different events`() {
        // Arrange
        val event1 = DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(listOf("TX001"))
        val event2 = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess
        val event3 = DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed("E001", "Error")

        // Act
        val (state1, _) = reducer.reduce(initialState, event1)
        val (state2, _) = reducer.reduce(state1, event2)
        val (state3, _) = reducer.reduce(state2, event3)

        // Assert
        assertEquals(initialState, state1)
        assertEquals(initialState, state2)
        assertEquals(initialState, state3)
        // All states should be the same reference since reducer doesn't modify state
        assertEquals(state1, state2)
        assertEquals(state2, state3)
    }
}
