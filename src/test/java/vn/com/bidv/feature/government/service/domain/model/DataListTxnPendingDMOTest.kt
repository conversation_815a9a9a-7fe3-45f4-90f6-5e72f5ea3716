package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.Gson
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Test

class DataListTxnPendingDMOTest {

    private val gson = Gson()

    @Test
    fun `DataListTxnPendingDMO should serialize to JSON correctly`() {
        // Arrange
        val txnPendingItem = TxnPendingListDMO(
            txnId = "TX123456789",
            debitAccNo = "**********",
            taxCode = "**********",
            declarationNo = "DECL001",
            amount = "1000000",
            ccy = "VND",
            batchNo = "BATCH001",
            status = "PENDING",
            createdDate = "2024-01-15 10:30:00",
            treasuryCode = "TREASURY001",
            treasuryName = "Test Treasury",
            admAreaCode = "AREA001",
            admAreaName = "Test Area",
            revAccCode = "REV001",
            revAccName = "Revenue Account",
            revAuthCode = "AUTH001",
            revAuthName = "Test Authority",
            bbCode = "BB001",
            statusName = "Pending Approval"
        )

        val dataList = DataListTxnPendingDMO(
            items = listOf(txnPendingItem),
            total = 1L
        )

        // Act
        val json = gson.toJson(dataList)

        // Assert
        assertNotNull(json)
        assert(json.contains("\"items\""))
        assert(json.contains("\"total\""))
        assert(json.contains("\"txnId\":\"TX123456789\""))
        assert(json.contains("\"debitAccNo\":\"**********\""))
        assert(json.contains("\"amount\":\"1000000\""))
    }

    @Test
    fun `DataListTxnPendingDMO should deserialize from JSON correctly`() {
        // Arrange
        val json = """
            {
                "items": [
                    {
                        "txnId": "TX123456789",
                        "debitAccNo": "**********",
                        "taxCode": "**********",
                        "declarationNo": "DECL001",
                        "amount": "1000000",
                        "ccy": "VND",
                        "batchNo": "BATCH001",
                        "status": "PENDING",
                        "createdDate": "2024-01-15 10:30:00",
                        "treasuryCode": "TREASURY001",
                        "treasuryName": "Test Treasury",
                        "admAreaCode": "AREA001",
                        "admAreaName": "Test Area",
                        "revAccCode": "REV001",
                        "revAccName": "Revenue Account",
                        "revAuthCode": "AUTH001",
                        "revAuthName": "Test Authority",
                        "bbCode": "BB001",
                        "statusName": "Pending Approval"
                    }
                ],
                "total": 1
            }
        """.trimIndent()

        // Act
        val result = gson.fromJson(json, DataListTxnPendingDMO::class.java)

        // Assert
        assertNotNull(result)
        assertEquals(1L, result.total)
        assertNotNull(result.items)
        assertEquals(1, result.items?.size)

        val item = result.items?.first()
        assertEquals("TX123456789", item?.txnId)
        assertEquals("**********", item?.debitAccNo)
        assertEquals("**********", item?.taxCode)
        assertEquals("DECL001", item?.declarationNo)
        assertEquals("1000000", item?.amount)
        assertEquals("VND", item?.ccy)
        assertEquals("BATCH001", item?.batchNo)
        assertEquals("PENDING", item?.status)
        assertEquals("2024-01-15 10:30:00", item?.createdDate)
        assertEquals("TREASURY001", item?.treasuryCode)
        assertEquals("Test Treasury", item?.treasuryName)
        assertEquals("AREA001", item?.admAreaCode)
        assertEquals("Test Area", item?.admAreaName)
        assertEquals("REV001", item?.revAccCode)
        assertEquals("Revenue Account", item?.revAccName)
        assertEquals("AUTH001", item?.revAuthCode)
        assertEquals("Test Authority", item?.revAuthName)
        assertEquals("BB001", item?.bbCode)
        assertEquals("Pending Approval", item?.statusName)
    }

    @Test
    fun `DataListTxnPendingDMO should handle null values correctly`() {
        // Arrange & Act
        val dataList = DataListTxnPendingDMO(
            items = null,
            total = null
        )

        // Assert
        assertNull(dataList.items)
        assertNull(dataList.total)
    }

    @Test
    fun `DataListTxnPendingDMO should handle empty list correctly`() {
        // Arrange & Act
        val dataList = DataListTxnPendingDMO(
            items = emptyList(),
            total = 0L
        )

        // Assert
        assertNotNull(dataList.items)
        assertEquals(0, dataList.items?.size)
        assertEquals(0L, dataList.total)
    }

    @Test
    fun `TxnPendingListDMO should create instance with all null values by default`() {
        // Arrange & Act
        val txnPending = TxnPendingListDMO()

        // Assert
        assertNull(txnPending.txnId)
        assertNull(txnPending.debitAccNo)
        assertNull(txnPending.taxCode)
        assertNull(txnPending.declarationNo)
        assertNull(txnPending.amount)
        assertNull(txnPending.ccy)
        assertNull(txnPending.batchNo)
        assertNull(txnPending.status)
        assertNull(txnPending.createdDate)
        assertNull(txnPending.treasuryCode)
        assertNull(txnPending.treasuryName)
        assertNull(txnPending.admAreaCode)
        assertNull(txnPending.admAreaName)
        assertNull(txnPending.revAccCode)
        assertNull(txnPending.revAccName)
        assertNull(txnPending.revAuthCode)
        assertNull(txnPending.revAuthName)
        assertNull(txnPending.bbCode)
        assertNull(txnPending.statusName)
    }

    @Test
    fun `TxnPendingListDMO should serialize and deserialize correctly`() {
        // Arrange
        val original = TxnPendingListDMO(
            txnId = "TX987654321",
            debitAccNo = "**********",
            taxCode = "**********",
            declarationNo = "DECL002",
            amount = "5000000",
            ccy = "USD",
            batchNo = "BATCH002",
            status = "APPROVED",
            createdDate = "2024-01-20 14:45:00",
            treasuryCode = "TREASURY002",
            treasuryName = "Main Treasury",
            admAreaCode = "AREA002",
            admAreaName = "Central Area",
            revAccCode = "REV002",
            revAccName = "Main Revenue Account",
            revAuthCode = "AUTH002",
            revAuthName = "Central Authority",
            bbCode = "BB002",
            statusName = "Approved"
        )

        // Act
        val json = gson.toJson(original)
        val deserialized = gson.fromJson(json, TxnPendingListDMO::class.java)

        // Assert
        assertEquals(original, deserialized)
        assertEquals(original.txnId, deserialized.txnId)
        assertEquals(original.debitAccNo, deserialized.debitAccNo)
        assertEquals(original.taxCode, deserialized.taxCode)
        assertEquals(original.declarationNo, deserialized.declarationNo)
        assertEquals(original.amount, deserialized.amount)
        assertEquals(original.ccy, deserialized.ccy)
        assertEquals(original.batchNo, deserialized.batchNo)
        assertEquals(original.status, deserialized.status)
        assertEquals(original.createdDate, deserialized.createdDate)
        assertEquals(original.treasuryCode, deserialized.treasuryCode)
        assertEquals(original.treasuryName, deserialized.treasuryName)
        assertEquals(original.admAreaCode, deserialized.admAreaCode)
        assertEquals(original.admAreaName, deserialized.admAreaName)
        assertEquals(original.revAccCode, deserialized.revAccCode)
        assertEquals(original.revAccName, deserialized.revAccName)
        assertEquals(original.revAuthCode, deserialized.revAuthCode)
        assertEquals(original.revAuthName, deserialized.revAuthName)
        assertEquals(original.bbCode, deserialized.bbCode)
        assertEquals(original.statusName, deserialized.statusName)
    }

    @Test
    fun `DataListTxnPendingDMO should support data class operations`() {
        // Arrange
        val txnPending1 = TxnPendingListDMO(txnId = "TX001", amount = "1000")
        val txnPending2 = TxnPendingListDMO(txnId = "TX002", amount = "2000")

        val dataList1 = DataListTxnPendingDMO(
            items = listOf(txnPending1),
            total = 1L
        )

        val dataList2 = DataListTxnPendingDMO(
            items = listOf(txnPending1),
            total = 1L
        )

        val dataList3 = DataListTxnPendingDMO(
            items = listOf(txnPending2),
            total = 1L
        )

        // Act & Assert - Test equals
        assertEquals(dataList1, dataList2)
        assertNotEquals(dataList1, dataList3)

        // Test hashCode
        assertEquals(dataList1.hashCode(), dataList2.hashCode())
        assertNotEquals(dataList1.hashCode(), dataList3.hashCode())

        // Test toString
        assertNotNull(dataList1.toString())
        assert(dataList1.toString().contains("DataListTxnPendingDMO"))

        // Test copy
        val copiedDataList = dataList1.copy(total = 2L)
        assertEquals(dataList1.items, copiedDataList.items)
        assertEquals(2L, copiedDataList.total)
        assertNotEquals(dataList1.total, copiedDataList.total)
    }

    @Test
    fun `TxnPendingListDMO should support data class operations`() {
        // Arrange
        val txnPending1 = TxnPendingListDMO(
            txnId = "TX001",
            amount = "1000",
            status = "PENDING"
        )

        val txnPending2 = TxnPendingListDMO(
            txnId = "TX001",
            amount = "1000",
            status = "PENDING"
        )

        val txnPending3 = TxnPendingListDMO(
            txnId = "TX002",
            amount = "2000",
            status = "APPROVED"
        )

        // Act & Assert - Test equals
        assertEquals(txnPending1, txnPending2)
        assertNotEquals(txnPending1, txnPending3)

        // Test hashCode
        assertEquals(txnPending1.hashCode(), txnPending2.hashCode())
        assertNotEquals(txnPending1.hashCode(), txnPending3.hashCode())

        // Test toString
        assertNotNull(txnPending1.toString())
        assert(txnPending1.toString().contains("TxnPendingListDMO"))

        // Test copy
        val copiedTxnPending = txnPending1.copy(status = "APPROVED")
        assertEquals(txnPending1.txnId, copiedTxnPending.txnId)
        assertEquals(txnPending1.amount, copiedTxnPending.amount)
        assertEquals("APPROVED", copiedTxnPending.status)
        assertNotEquals(txnPending1.status, copiedTxnPending.status)
    }

    @Test
    fun `DataListTxnPendingDMO should handle large datasets correctly`() {
        // Arrange
        val largeItemList = (1..1000).map { index ->
            TxnPendingListDMO(
                txnId = "TX${String.format("%06d", index)}",
                amount = "${index * 1000}",
                status = if (index % 2 == 0) "APPROVED" else "PENDING"
            )
        }

        val dataList = DataListTxnPendingDMO(
            items = largeItemList,
            total = 1000L
        )

        // Act
        val json = gson.toJson(dataList)
        val deserialized = gson.fromJson(json, DataListTxnPendingDMO::class.java)

        // Assert
        assertEquals(1000L, deserialized.total)
        assertEquals(1000, deserialized.items?.size)
        assertEquals("TX000001", deserialized.items?.first()?.txnId)
        assertEquals("TX001000", deserialized.items?.last()?.txnId)
        assertEquals("PENDING", deserialized.items?.first()?.status)
        assertEquals("APPROVED", deserialized.items?.last()?.status)
    }
}
