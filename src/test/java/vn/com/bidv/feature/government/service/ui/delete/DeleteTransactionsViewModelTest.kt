package vn.com.bidv.feature.government.service.ui.delete

import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertNotNull
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.usecase.DeleteTransactionsUseCase
import vn.com.bidv.feature.government.service.model.TxnDeleteResultDMO
import vn.com.bidv.sdkbase.domain.DomainResult

class DeleteTransactionsViewModelTest {

    private val mockDeleteTransactionsUseCase: DeleteTransactionsUseCase = mockk()
    private val viewModel = DeleteTransactionsViewModel(mockDeleteTransactionsUseCase)

    @Test
    fun `viewModel should be instantiated correctly`() {
        // Assert
        assertNotNull(viewModel)
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with success result`() = runBlocking {
        // Arrange
        val txnIds = listOf("TX001", "TX002")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val successResult = TxnDeleteResultDMO(errorCode = null, errorMessage = "Success")
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(successResult)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert
        verify { onResult(DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess) }
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with business error`() = runBlocking {
        // Arrange
        val txnIds = listOf("TX001")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val errorResult = TxnDeleteResultDMO(errorCode = "E001", errorMessage = "Business error")
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(errorResult)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert
        verify { 
            onResult(
                DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed("E001", "Business error")
            ) 
        }
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with null data`() = runBlocking {
        // Arrange
        val txnIds = listOf("TX001")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(null)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert - Should not call onResult when data is null
        verify(exactly = 0) { onResult(any()) }
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with domain error`() = runBlocking {
        // Arrange
        val txnIds = listOf("TX001")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Error("E002", "Network error")

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert
        verify { 
            onResult(
                DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed("E002", "Network error")
            ) 
        }
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with null error details`() = runBlocking {
        // Arrange
        val txnIds = listOf("TX001")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Error(null, null)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert
        verify { 
            onResult(
                DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(null, null)
            ) 
        }
    }

    @Test
    fun `handleEffect should handle RequestDeleteSuccess effect with noop`() {
        // Arrange
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteSuccess
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert - Should not call onResult for noop case
        verify(exactly = 0) { onResult(any()) }
    }

    @Test
    fun `handleEffect should handle RequestDeleteFailed effect with noop`() {
        // Arrange
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed("E001", "Error")
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert - Should not call onResult for noop case
        verify(exactly = 0) { onResult(any()) }
    }

    @Test
    fun `handleEffect should handle empty transaction ids list`() = runBlocking {
        // Arrange
        val txnIds = emptyList<String>()
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val successResult = TxnDeleteResultDMO(errorCode = null, errorMessage = "Success")
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(successResult)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Assert
        verify { onResult(DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess) }
    }
}
