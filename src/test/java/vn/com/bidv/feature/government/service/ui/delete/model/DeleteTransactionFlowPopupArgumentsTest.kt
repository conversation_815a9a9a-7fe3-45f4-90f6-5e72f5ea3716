package vn.com.bidv.feature.government.service.ui.delete.model

import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotEquals
import org.junit.Assert.assertNotNull
import org.junit.Test
import vn.com.bidv.feature.government.service.model.DeleteRoot

class DeleteTransactionFlowPopupArgumentsTest {

    @Test
    fun `constructor should create instance with provided values`() {
        // Arrange
        val txnIds = listOf("TX001", "TX002", "TX003")
        val deleteRoot = DeleteRoot.ListScreen

        // Act
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = txnIds,
            deleteRoot = deleteRoot
        )

        // Assert
        assertEquals(txnIds, arguments.txnIds)
        assertEquals(deleteRoot, arguments.deleteRoot)
        assertEquals(3, arguments.txnIds.size)
        assertEquals("TX001", arguments.txnIds[0])
        assertEquals("TX002", arguments.txnIds[1])
        assertEquals("TX003", arguments.txnIds[2])
    }

    @Test
    fun `should handle empty transaction ids list`() {
        // Arrange
        val txnIds = emptyList<String>()
        val deleteRoot = DeleteRoot.DetailScreen

        // Act
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = txnIds,
            deleteRoot = deleteRoot
        )

        // Assert
        assertEquals(emptyList<String>(), arguments.txnIds)
        assertEquals(DeleteRoot.DetailScreen, arguments.deleteRoot)
        assertEquals(0, arguments.txnIds.size)
    }

    @Test
    fun `should handle single transaction id`() {
        // Arrange
        val txnIds = listOf("TX123456789")
        val deleteRoot = DeleteRoot.DetailScreen

        // Act
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = txnIds,
            deleteRoot = deleteRoot
        )

        // Assert
        assertEquals(1, arguments.txnIds.size)
        assertEquals("TX123456789", arguments.txnIds[0])
        assertEquals(DeleteRoot.DetailScreen, arguments.deleteRoot)
    }

    @Test
    fun `should serialize to JSON correctly`() {
        // Arrange
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )

        // Act
        val json = Json.encodeToString(arguments)

        // Assert
        assertNotNull(json)
        assert(json.contains("\"txnIds\""))
        assert(json.contains("\"deleteRoot\""))
        assert(json.contains("\"TX001\""))
        assert(json.contains("\"TX002\""))
        assert(json.contains("\"ListScreen\""))
    }

    @Test
    fun `should deserialize from JSON correctly`() {
        // Arrange
        val json = """
            {
                "txnIds": ["TX001", "TX002", "TX003"],
                "deleteRoot": "DetailScreen"
            }
        """.trimIndent()

        // Act
        val arguments = Json.decodeFromString<DeleteTransactionFlowPopupArguments>(json)

        // Assert
        assertEquals(3, arguments.txnIds.size)
        assertEquals("TX001", arguments.txnIds[0])
        assertEquals("TX002", arguments.txnIds[1])
        assertEquals("TX003", arguments.txnIds[2])
        assertEquals(DeleteRoot.DetailScreen, arguments.deleteRoot)
    }

    @Test
    fun `should handle round-trip serialization correctly`() {
        // Arrange
        val original = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX987654321", "TX123456789"),
            deleteRoot = DeleteRoot.ListScreen
        )

        // Act
        val json = Json.encodeToString(original)
        val deserialized = Json.decodeFromString<DeleteTransactionFlowPopupArguments>(json)

        // Assert
        assertEquals(original, deserialized)
        assertEquals(original.txnIds, deserialized.txnIds)
        assertEquals(original.deleteRoot, deserialized.deleteRoot)
    }

    @Test
    fun `should support data class equals operation`() {
        // Arrange
        val arguments1 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )
        val arguments2 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )
        val arguments3 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX003", "TX004"),
            deleteRoot = DeleteRoot.DetailScreen
        )

        // Act & Assert
        assertEquals(arguments1, arguments2)
        assertNotEquals(arguments1, arguments3)
        assertNotEquals(arguments2, arguments3)
    }

    @Test
    fun `should support data class hashCode operation`() {
        // Arrange
        val arguments1 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )
        val arguments2 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )
        val arguments3 = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.DetailScreen
        )

        // Act & Assert
        assertEquals(arguments1.hashCode(), arguments2.hashCode())
        assertNotEquals(arguments1.hashCode(), arguments3.hashCode())
    }

    @Test
    fun `should support data class toString operation`() {
        // Arrange
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )

        // Act
        val toString = arguments.toString()

        // Assert
        assertNotNull(toString)
        assert(toString.contains("DeleteTransactionFlowPopupArguments"))
        assert(toString.contains("txnIds"))
        assert(toString.contains("deleteRoot"))
        assert(toString.contains("TX001"))
        assert(toString.contains("TX002"))
        assert(toString.contains("ListScreen"))
    }

    @Test
    fun `should support data class copy operation`() {
        // Arrange
        val original = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001", "TX002"),
            deleteRoot = DeleteRoot.ListScreen
        )

        // Act
        val copiedWithNewTxnIds = original.copy(txnIds = listOf("TX003", "TX004"))
        val copiedWithNewDeleteRoot = original.copy(deleteRoot = DeleteRoot.DetailScreen)
        val copiedWithBothChanged = original.copy(
            txnIds = listOf("TX005"),
            deleteRoot = DeleteRoot.DetailScreen
        )

        // Assert
        assertEquals(listOf("TX003", "TX004"), copiedWithNewTxnIds.txnIds)
        assertEquals(DeleteRoot.ListScreen, copiedWithNewTxnIds.deleteRoot)

        assertEquals(listOf("TX001", "TX002"), copiedWithNewDeleteRoot.txnIds)
        assertEquals(DeleteRoot.DetailScreen, copiedWithNewDeleteRoot.deleteRoot)

        assertEquals(listOf("TX005"), copiedWithBothChanged.txnIds)
        assertEquals(DeleteRoot.DetailScreen, copiedWithBothChanged.deleteRoot)

        // Original should remain unchanged
        assertEquals(listOf("TX001", "TX002"), original.txnIds)
        assertEquals(DeleteRoot.ListScreen, original.deleteRoot)
    }

    @Test
    fun `should work with both DeleteRoot enum values`() {
        // Arrange & Act
        val argumentsWithListScreen = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX001"),
            deleteRoot = DeleteRoot.ListScreen
        )
        val argumentsWithDetailScreen = DeleteTransactionFlowPopupArguments(
            txnIds = listOf("TX002"),
            deleteRoot = DeleteRoot.DetailScreen
        )

        // Assert
        assertEquals(DeleteRoot.ListScreen, argumentsWithListScreen.deleteRoot)
        assertEquals(DeleteRoot.DetailScreen, argumentsWithDetailScreen.deleteRoot)
        assertNotEquals(argumentsWithListScreen.deleteRoot, argumentsWithDetailScreen.deleteRoot)
    }

    @Test
    fun `should handle large transaction ids list`() {
        // Arrange
        val largeTxnIds = (1..100).map { "TX${String.format("%06d", it)}" }
        val arguments = DeleteTransactionFlowPopupArguments(
            txnIds = largeTxnIds,
            deleteRoot = DeleteRoot.ListScreen
        )

        // Act
        val json = Json.encodeToString(arguments)
        val deserialized = Json.decodeFromString<DeleteTransactionFlowPopupArguments>(json)

        // Assert
        assertEquals(100, arguments.txnIds.size)
        assertEquals("TX000001", arguments.txnIds.first())
        assertEquals("TX000100", arguments.txnIds.last())
        assertEquals(arguments, deserialized)
        assertEquals(100, deserialized.txnIds.size)
    }
}
