package vn.com.bidv.feature.government.service.model

import com.google.gson.Gson
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ListTransactionRuleFilter
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import java.util.Date

data class ListCustomsDutiesRuleFilter(
    override var search: String? = null,
    var maxAmount: String? = null,
    var minAmount: String? = null,
    var debit: String? = null,
    var tax: String? = null,
    var declaration: String? = null,
    var batch: String? = null,
    var startDate: Date? = null,
    var endDate: Date? = null,
    var listCurrencySelected: List<String>? = null,
    var listStatusSelected: List<String>? = null,
) : ListTransactionRuleFilter {
    override fun getBadgeNumber(): Int {
        var count = 0

        if (!search.isNullOrEmpty()) count++
        if (!maxAmount.isNullOrEmpty()) count++
        if (!minAmount.isNullOrEmpty()) count++
        if (!debit.isNullOrEmpty()) count++
        if (!tax.isNullOrEmpty()) count++
        if (!declaration.isNullOrEmpty()) count++
        if (!batch.isNullOrEmpty()) count++
        if (startDate != null) count++
        if (!listCurrencySelected.isNullOrEmpty()) count++
        if (!listStatusSelected.isNullOrEmpty()) count++

        return count

    }

    override fun <R : ListTransactionRuleFilter> copyGson(): R {
        return try {
            val json = Gson().toJson(this)
            Gson().fromJson(json, this::class.java) as R
        } catch (e: Exception) {
            ListCustomsDutiesRuleFilter() as R
        }
    }

    override fun <R : ListTransactionRuleFilter> copySearch(search: String?): R {
        return this.copy(search = search) as R
    }

    fun setValueTo(
        search: String? = this.search, // Có thể giữ giá trị hiện tại nếu không truyền vào
        startDate: Date?,
        endDate: Date?,
        debit: String?,
        tax: String?,
        declaration: String?,
        batch: String?,
        minAmount: String?,
        maxAmount: String?,
        listCurrencySelected: List<String>?,
        listStatusSelected: List<String>?
    ) {
        // Cập nhật các thuộc tính của instance hiện tại
        this.search = search
        this.startDate = startDate
        this.endDate = endDate
        this.debit = debit
        this.tax = tax
        this.declaration = declaration
        this.batch = batch
        this.minAmount = minAmount
        this.maxAmount = maxAmount
        this.listCurrencySelected = listCurrencySelected
        this.listStatusSelected = listStatusSelected
    }

}
