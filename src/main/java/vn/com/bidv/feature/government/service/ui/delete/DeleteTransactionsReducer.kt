package vn.com.bidv.feature.government.service.ui.delete

import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class DeleteTransactionsReducer :
    Reducer<DeleteTransactionsReducer.ReducerViewState, DeleteTransactionsReducer.ReducerViewEvent, DeleteTransactionsReducer.ReducerViewEffect> {
    class ReducerViewState() : ViewState

    sealed class ReducerViewEvent : ViewEvent {
        data class UserRequestDeleteTxns(val txnIds: List<String>) : ReducerViewEvent()
        data object RequestDeleteSuccess : ReducerViewEvent()
        data class RequestDeleteFailed(val errorCode: String?, val errorMessage: String?) :
            ReducerViewEvent()
    }

    sealed class ReducerViewEffect : SideEffect {
        data class UserRequestDeleteTxns(val txnIds: List<String>) : ReducerViewEffect()
        data object RequestDeleteSuccess : ReducerViewEffect(), UIEffect
        data class RequestDeleteFailed(val errorCode: String?, val errorMessage: String?) :
            ReducerViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: ReducerViewState,
        event: ReducerViewEvent
    ): Pair<ReducerViewState, ReducerViewEffect?> {
        return when (event) {
            is ReducerViewEvent.UserRequestDeleteTxns -> {
                previousState to ReducerViewEffect.UserRequestDeleteTxns(event.txnIds)
            }

            is ReducerViewEvent.RequestDeleteSuccess -> {
                previousState to ReducerViewEffect.RequestDeleteSuccess
            }

            is ReducerViewEvent.RequestDeleteFailed -> {
                previousState to ReducerViewEffect.RequestDeleteFailed(
                    event.errorCode,
                    event.errorMessage
                )
            }
        }
    }
}