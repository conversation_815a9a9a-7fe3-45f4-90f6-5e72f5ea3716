package vn.com.bidv.feature.government.service.ui.customsdutiesandfees

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.segmentcontrol.tab.IBankTabRow
import vn.com.bidv.designsystem.component.segmentcontrol.tab.TabItem
import vn.com.bidv.designsystem.component.segmentcontrol.tab.TabType
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.alltransaction.ListAllCustomsDutiesScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.tabcontent.pendingtransaction.ListPendingCustomsDutiesScreen

@Composable
fun ListCustomsDutiesMainScreen(navController: NavHostController) {
    val viewModel: ListCustomsDutiesMainViewModel = hiltViewModel()

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(titleTopAppBar = stringResource(vn.com.bidv.localization.R.string.thue_phi_hai_quan)),
        handleSideEffect = { }
    ) { uiState, onEvent ->
        CustomsDutiesAndFeesMainTab(
            navController = navController,
            viewModel = viewModel,
            uiState = uiState,
            onEvent = onEvent,
        )
    }

}

@Composable
fun CustomsDutiesAndFeesMainTab(
    navController: NavHostController,
    viewModel: ListCustomsDutiesMainViewModel,
    uiState: ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState,
    onEvent: (ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent) -> Unit,
) {

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        //tab layout
        val listTab = listOf(
            TabItem(stringResource(vn.com.bidv.localization.R.string.tat_ca_giao_dich)),
            TabItem(stringResource(vn.com.bidv.localization.R.string.giao_dich_cho_xu_ly))
        )

        Column {
            IBankTabRow(
                listTab = listTab,
                tabType = TabType.UNDERLINE,
                fullWidth = true,
                indent = true,
                showBackground = true,
                selectedTabIndex = uiState.tabLayoutIndex
            ) { currentTab ->
                onEvent(
                    ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent.SelectedTab(
                        indexTab = listTab.indexOf(currentTab)
                    )
                )
            }
        }
        when(uiState.tabLayoutIndex) {
            0 -> ListAllCustomsDutiesScreen(navController)
            1 -> ListPendingCustomsDutiesScreen(navController)
        }
    }

}