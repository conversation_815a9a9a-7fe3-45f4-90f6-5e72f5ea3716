package vn.com.bidv.feature.government.service.ui.delete

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.navOptions
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.sdkbase.navigation.IBankMainRouting

@Composable
fun DeleteTransactionFlowPopup(
    navHostController: NavHostController,
    txnIds: List<String>
) {
    val context = LocalContext.current
    val deleteTransactionsViewModel: DeleteTransactionsViewModel = hiltViewModel()
    var deleteFailedReason by remember { mutableStateOf<String?>(null) }

    BaseMVIScreen(
        viewModel = deleteTransactionsViewModel,
        handleSideEffect = {
            when (it) {
                is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed -> {
                    deleteFailedReason = it.errorMessage
                }

                is DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteSuccess -> {
                    deleteTransactionsViewModel.showSnackBar(
                        IBankSnackBarInfo(
                            message = context.getString(R.string.xoa_giao_dich_thanh_cong),
                            primaryButtonText = context.getString(R.string.close)
                        )
                    )
                    exitFlowAfterOperation(
                        deleteTransactionsViewModel = deleteTransactionsViewModel,
                        navController = navHostController
                    )
                }

                is DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns -> { /*noop*/
                }
            }
        },
    ) { _, viewEvent ->
        DeleteTransactionFlowContent(
            txnIds = txnIds,
            onEvent = viewEvent,
            onCloseFlow = { isFlowComplete ->
                if (isFlowComplete) {
                    exitFlowAfterOperation(
                        deleteTransactionsViewModel = deleteTransactionsViewModel,
                        navController = navHostController
                    )
                } else {
                    navHostController.popBackStack()
                }
            },
            deleteFailedReason = deleteFailedReason,
        )
    }
}

@Composable
private fun DeleteTransactionFlowContent(
    modifier: Modifier = Modifier,
    txnIds: List<String>,
    onEvent: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit,
    onCloseFlow: (isFlowComplete: Boolean) -> Unit,
    deleteFailedReason: String?,
) {
    IBankModalConfirm(
        modifier = modifier,
        modalConfirmType = ModalConfirmType.Question,
        title = stringResource(R.string.xac_nhan_xoa),
        supportingText = if (txnIds.size == 1)
            stringResource(R.string.quy_khach_co_chac_chan_xoa_giao_dich)
        else
            stringResource(R.string.quy_khach_co_chac_chan_xoa_d_giao_dich, txnIds.size),
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(R.string.dong_y),
                onClick = {
                    onEvent(
                        DeleteTransactionsReducer.ReducerViewEvent.UserRequestDeleteTxns(
                            txnIds
                        )
                    )
                }
            ),
            DialogButtonInfo(
                label = stringResource(R.string.huy),
                onClick = {
                    onCloseFlow(false)
                }
            )
        )
    )

    if (deleteFailedReason != null) {
        val total = txnIds.size
        val totalFailed = txnIds.size
        val failedCount = if (total > 1) {
            "${totalFailed}/${total} ${stringResource(R.string.giao_dich).lowercase()}"
        } else {
            null
        }

        IBankModalConfirm(
            title = stringResource(R.string.xoa_khong_thanh_cong),
            modalConfirmType = ModalConfirmType.Error,
            mainContent = failedCount,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.xac_nhan),
                    onClick = {
                        onCloseFlow(true)
                    }
                )
            )
        )
    }
}

private fun exitFlowAfterOperation(
    deleteTransactionsViewModel: DeleteTransactionsViewModel,
    navController: NavHostController
) {
    val key = ReloadKey(
        ReloadModuleKey.GOVERNMENT_SERVICE,
        ReloadFunctionKey.LIST_PENDING
    )
    deleteTransactionsViewModel.requestReloadData(key)

    navController.navigate(
        route = IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route,
        navOptions = navOptions {
            popUpTo(IBankMainRouting.GovernmentServiceRoute.GovernmentServiceMainRoute.route) {
                inclusive = false
            }
            launchSingleTop = true
            restoreState = true
        }
    )
}
