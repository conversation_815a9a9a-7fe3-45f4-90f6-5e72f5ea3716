package vn.com.bidv.feature.government.service.ui.screen.pendingtransactiondetail.model

import com.google.gson.annotations.SerializedName

/**
 * @param admAreaCode Mã địa bàn hành chính
 * @param admAreaName Tên địa bàn hành chính
 * @param amount Số tiền
 * @param bbCode Ngân hàng thụ hưởng
 * @param ccCode Mã loại tiền hải quan
 * @param ccName Tên loại tiền hải quan
 * @param ccy Mã đơn vị tiền tệ
 * @param chapterCode Mã chương
 * @param chapterName Tên chương
 * @param declarationDate Ngày tờ khai
 * @param declarationNumber Số tờ khai
 * @param ecCode Mã nội dung kinh tế
 * @param ecName Tên nội dung kinh tế
 * @param eiTypeCode Mã loại hình xuất nhập khẩu
 * @param eiTypeName Tên loại hình xuất nhập khẩu
 * @param revAccCode Mã tài khoản thu
 * @param revAccName Tên tài khoản thu
 * @param revAuthCode Mã cơ quan thu
 * @param revAuthName Tên cơ quan thu
 * @param payerType Loại hình người nộp thuế
 * @param taxTypeCode Mã sắc thuế
 * @param taxTypeName Tên sắc thuế
 * @param transDesc Diễn giải giao dịch
 * @param treasuryCode Mã kho bạc
 * @param treasuryName Tên kho bạc
 */

data class TaxItemDMO (

        /* Mã địa bàn hành chính */
        @SerializedName("admAreaCode")
        val admAreaCode: String? = null,

        /* Tên địa bàn hành chính */
        @SerializedName("admAreaName")
        val admAreaName: String? = null,

        /* Số tiền */
        @SerializedName("amount")
        val amount: String? = null,

        /* Ngân hàng thụ hưởng */
        @SerializedName("bbCode")
        val bbCode: String? = null,

        /* Mã loại tiền hải quan */
        @SerializedName("ccCode")
        val ccCode: String? = null,

        /* Tên loại tiền hải quan */
        @SerializedName("ccName")
        val ccName: String? = null,

        /* Loại tiền */
        @SerializedName("ccy")
        val ccy: String? = null,

        /* Mã chương */
        @SerializedName("chapterCode")
        val chapterCode: String? = null,

        /* Tên chương */
        @SerializedName("chapterName")
        val chapterName: String? = null,

        /* Ngày tờ khai */
        @SerializedName("declarationDate")
        val declarationDate: String? = null,

        /* Số tờ khai */
        @SerializedName("declarationNo")
        val declarationNumber: String? = null,

        /* Mã nội dung kinh tế */
        @SerializedName("ecCode")
        val ecCode: String? = null,

        /* Tên nội dung kinh tế */
        @SerializedName("ecName")
        val ecName: String? = null,

        /* Mã loại hình xuất nhập khẩu */
        @SerializedName("eiTypeCode")
        val eiTypeCode: String? = null,

        /* Tên loại hình xuất nhập khẩu */
        @SerializedName("eiTypeName")
        val eiTypeName: String? = null,

        /* Mã tài khoản thu */
        @SerializedName("revAccCode")
        val revAccCode: String? = null,

        /* Tên tài khoản thu */
        @SerializedName("revAccName")
        val revAccName: String? = null,

        /* Mã cơ quan thu */
        @SerializedName("revAuthCode")
        val revAuthCode: String? = null,

        /* Tên cơ quan thu */
        @SerializedName("revAuthName")
        val revAuthName: String? = null,

        /* Loại hình người nộp thuế */
        @SerializedName("payerType")
        val payerType: String? = null,

        /* Mã sắc thuế */
        @SerializedName("taxTypeCode")
        val taxTypeCode: String? = null,

        /* Tên sắc thuế */
        @SerializedName("taxTypeName")
        val taxTypeName: String? = null,

        /* Diễn giải giao dịch */
        @SerializedName("transDesc")
        val transDesc: String? = null,

        /* Mã kho bạc */
        @SerializedName("treasuryCode")
        val treasuryCode: String? = null,

        /* Tên kho bạc */
        @SerializedName("treasuryName")
        val treasuryName: String? = null

)