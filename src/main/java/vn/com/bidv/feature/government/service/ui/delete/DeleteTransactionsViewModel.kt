package vn.com.bidv.feature.government.service.ui.delete

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.government.service.data.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.usecase.DeleteTransactionsUseCase
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class DeleteTransactionsViewModel @Inject constructor(
    private val deleteTransactionsUseCase: DeleteTransactionsUseCase,
) : ViewModelIBankBase<
        DeleteTransactionsReducer.ReducerViewState,
        DeleteTransactionsReducer.ReducerViewEvent,
        DeleteTransactionsReducer.ReducerViewEffect
        >(
    initialState = DeleteTransactionsReducer.ReducerViewState(),
    reducer = DeleteTransactionsReducer(),
) {
    override fun handleEffect(
        sideEffect: DeleteTransactionsReducer.ReducerViewEffect,
        onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        val data = it.data ?: return@callDomain
                        if (!data.isSuccess()) {
                            onResult(
                                DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(
                                    data.errorCode, data.errorMessage
                                )
                            )
                        } else {
                            onResult(
                                DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess
                            )
                        }
                    },
                    onFail = {
                        onResult(
                            DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed(
                                it?.errorCode, it?.errorMessage
                            )
                        )
                    }
                ) {
                    deleteTransactionsUseCase.invoke(sideEffect.txnIds)
                }
            }

            else -> {/*noop*/}
        }
    }
}