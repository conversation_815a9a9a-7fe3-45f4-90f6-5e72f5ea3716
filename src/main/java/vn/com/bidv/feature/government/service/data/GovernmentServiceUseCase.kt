package vn.com.bidv.feature.government.service.data

import vn.com.bidv.feature.government.service.data.governmentservice.model.Page
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDeleteReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.domain.model.DataListTxnPendingDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.feature.government.service.model.TxnDeleteResultDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.exts.formatDateToString
import javax.inject.Inject

class GovernmentServiceUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) {

    suspend fun getListPendingTransaction(
        rule: ListCustomsDutiesRuleFilter?,
        pageIndex: Int,
        pageSize: Int
    ): DomainResult<DataListTxnPendingDMO> {

        val request = TxnPendingListReq(
            page = Page(
                pageNum = pageIndex,
                pageSize = pageSize
            ),
            search = rule?.search,
            startDate = rule?.startDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            endDate = rule?.endDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            minAmount = rule?.minAmount,
            maxAmount = rule?.maxAmount,
            ccy = "VND",
            statuses = rule?.listStatusSelected,
            debitAccNo = rule?.debit,
            taxCode = rule?.tax,
            declarationNo = rule?.declaration,
            batchNo = rule?.batch
        )

        val result = governmentServiceRepository.getListPendingTransaction(request)
        return result.convert(DataListTxnPendingDMO::class.java)
    }

    fun getListCurrency(): DomainResult<List<String>> {
        return DomainResult.Success(listOf("VND"))
    }

    fun getListStatus(): DomainResult<List<TransactionStatusBase>> {
        return DomainResult.Success(
            listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)
        )
    }
}